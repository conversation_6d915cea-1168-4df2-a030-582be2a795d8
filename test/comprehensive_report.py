#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
综合报告生成器
生成主力资金监测的完整结论报告
"""

import sys
import os
sys.path.append('..')

from main_fund_monitor import MainFundMonitor
from sensitive_analysis import SensitiveMainFundMonitor
import matplotlib.pyplot as plt
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class ComprehensiveReportGenerator:
    """综合报告生成器"""
    
    def __init__(self):
        self.symbols = ['BTCUSDT', 'ETHUSDT', 'XRPUSDT']
        self.monitor = SensitiveMainFundMonitor()
    
    def generate_summary_chart(self, all_results):
        """生成总结图表"""
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('主力资金监测总结仪表板', fontsize=18, fontweight='bold')
        
        # 信号统计
        ax1 = axes[0, 0]
        signal_counts = {}
        for symbol, data in all_results.items():
            if not data['signals'].empty:
                counts = data['signals']['signal_type'].value_counts()
                signal_counts[symbol] = len(data['signals'])
            else:
                signal_counts[symbol] = 0
        
        bars = ax1.bar(signal_counts.keys(), signal_counts.values(), 
                      color=['#FF6B6B', '#4ECDC4', '#45B7D1'])
        ax1.set_title('各币种主力资金信号数量', fontsize=14, fontweight='bold')
        ax1.set_ylabel('信号数量', fontsize=12)
        
        # 在柱状图上添加数值标签
        for bar in bars:
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height,
                    f'{int(height)}', ha='center', va='bottom', fontsize=12)
        
        # 信号类型分布
        ax2 = axes[0, 1]
        all_signals = []
        for symbol, data in all_results.items():
            if not data['signals'].empty:
                all_signals.extend(data['signals']['signal_type'].tolist())
        
        if all_signals:
            signal_type_counts = pd.Series(all_signals).value_counts()
            colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4']
            wedges, texts, autotexts = ax2.pie(signal_type_counts.values, 
                                              labels=signal_type_counts.index,
                                              colors=colors[:len(signal_type_counts)],
                                              autopct='%1.1f%%',
                                              startangle=90)
            ax2.set_title('主力资金信号类型分布', fontsize=14, fontweight='bold')
        else:
            ax2.text(0.5, 0.5, '暂无信号数据', ha='center', va='center', 
                    fontsize=14, transform=ax2.transAxes)
            ax2.set_title('主力资金信号类型分布', fontsize=14, fontweight='bold')
        
        # 最近24小时活跃度
        ax3 = axes[1, 0]
        recent_activity = {}
        now = datetime.now()
        for symbol, data in all_results.items():
            if not data['signals'].empty:
                recent_signals = data['signals'][
                    data['signals']['timestamp'] > (now - timedelta(hours=24))
                ]
                recent_activity[symbol] = len(recent_signals)
            else:
                recent_activity[symbol] = 0
        
        bars = ax3.bar(recent_activity.keys(), recent_activity.values(),
                      color=['#FFD93D', '#6BCF7F', '#4D96FF'])
        ax3.set_title('最近24小时主力资金活跃度', fontsize=14, fontweight='bold')
        ax3.set_ylabel('信号数量', fontsize=12)
        
        for bar in bars:
            height = bar.get_height()
            ax3.text(bar.get_x() + bar.get_width()/2., height,
                    f'{int(height)}', ha='center', va='bottom', fontsize=12)
        
        # 置信度分析
        ax4 = axes[1, 1]
        confidence_data = []
        labels = []
        for symbol, data in all_results.items():
            if not data['signals'].empty and 'confidence' in data['signals'].columns:
                avg_confidence = data['signals']['confidence'].mean()
                confidence_data.append(avg_confidence)
                labels.append(symbol)
        
        if confidence_data:
            bars = ax4.bar(labels, confidence_data, color=['#FF9FF3', '#54A0FF', '#5F27CD'])
            ax4.set_title('平均信号置信度', fontsize=14, fontweight='bold')
            ax4.set_ylabel('置信度', fontsize=12)
            ax4.set_ylim(0, 1)
            
            for bar in bars:
                height = bar.get_height()
                ax4.text(bar.get_x() + bar.get_width()/2., height,
                        f'{height:.2f}', ha='center', va='bottom', fontsize=12)
        else:
            ax4.text(0.5, 0.5, '暂无置信度数据', ha='center', va='center',
                    fontsize=14, transform=ax4.transAxes)
            ax4.set_title('平均信号置信度', fontsize=14, fontweight='bold')
        
        plt.tight_layout()
        return fig
    
    def generate_detailed_conclusion(self, all_results):
        """生成详细结论"""
        conclusions = []
        
        # 总体分析
        total_signals = sum(len(data['signals']) for data in all_results.values())
        active_symbols = sum(1 for data in all_results.values() if not data['signals'].empty)
        
        conclusions.append("=" * 60)
        conclusions.append("主力资金监测方法结论报告")
        conclusions.append("=" * 60)
        conclusions.append("")
        
        # 方法论说明
        conclusions.append("📊 分析方法论:")
        conclusions.append("基于持仓量(OI)和多空比的综合分析法")
        conclusions.append("")
        conclusions.append("🔍 信号识别规则:")
        conclusions.append("1. 持仓上升 + 多空比下降 = 主力做多建仓")
        conclusions.append("   原理: 主力大额做多，吃掉大量空单挂单，使多空比下降")
        conclusions.append("2. 持仓上升 + 多空比上升 = 主力做空建仓") 
        conclusions.append("   原理: 主力大额做空，吃掉大量多单挂单，使多空比上升")
        conclusions.append("3. 持仓下降 + 多空比上升 = 主力平多仓")
        conclusions.append("   原理: 主力平多相当于卖出，吃掉散户多单挂单")
        conclusions.append("4. 持仓下降 + 多空比下降 = 主力平空仓")
        conclusions.append("   原理: 主力平空相当于买入，吃掉散户空单挂单")
        conclusions.append("")
        
        # 总体结果
        conclusions.append("📈 总体分析结果:")
        conclusions.append(f"• 监测币种: {len(self.symbols)} 个 (BTCUSDT, ETHUSDT, XRPUSDT)")
        conclusions.append(f"• 发现信号总数: {total_signals} 个")
        conclusions.append(f"• 活跃币种数量: {active_symbols} 个")
        conclusions.append("")
        
        # 各币种详细分析
        conclusions.append("💰 各币种详细分析:")
        conclusions.append("-" * 40)
        
        for symbol, data in all_results.items():
            conclusions.append(f"\n🔸 {symbol}:")
            
            if data['signals'].empty:
                conclusions.append("  ❌ 在分析期间内未发现明显的主力资金活动信号")
                conclusions.append("  📊 市场状态: 相对平静，主力资金活动较少")
            else:
                signals_df = data['signals']
                merged_data = data['merged_data']
                
                # 信号统计
                signal_counts = signals_df['signal_type'].value_counts()
                conclusions.append(f"  ✅ 发现主力资金信号: {len(signals_df)} 个")
                conclusions.append("  📊 信号类型分布:")
                for signal_type, count in signal_counts.items():
                    conclusions.append(f"     • {signal_type}: {count} 次")
                
                # 最近信号
                recent_signals = signals_df.tail(3)
                conclusions.append("  🕐 最近信号:")
                for _, signal in recent_signals.iterrows():
                    time_str = signal['timestamp'].strftime('%m-%d %H:%M')
                    confidence = signal.get('confidence', 0)
                    conclusions.append(f"     • {time_str} - {signal['signal_type']} (置信度: {confidence:.2f})")
                
                # 价格影响分析
                if 'price_change_pct' in signals_df.columns:
                    avg_price_impact = signals_df['price_change_pct'].mean()
                    conclusions.append(f"  📈 信号伴随的平均价格变化: {avg_price_impact:.2f}%")
                
                # 活跃度评估
                now = datetime.now()
                recent_24h = signals_df[signals_df['timestamp'] > (now - timedelta(hours=24))]
                activity_level = "高" if len(recent_24h) >= 2 else "中" if len(recent_24h) >= 1 else "低"
                conclusions.append(f"  🔥 最近24小时活跃度: {activity_level}")
        
        # 投资建议
        conclusions.append("\n" + "=" * 40)
        conclusions.append("💡 投资建议与注意事项:")
        conclusions.append("=" * 40)
        conclusions.append("1. 🎯 信号验证: 主力资金信号应结合价格走势、成交量等多维度验证")
        conclusions.append("2. ⚠️  风险控制: 大资金不一定总是正确，需要设置止损")
        conclusions.append("3. 📊 市场环境: BTC等大市值币种主力影响相对较小，山寨币更适用")
        conclusions.append("4. 🕐 时间周期: 建议关注持仓量缓慢上升但价格横盘的情况")
        conclusions.append("5. 🔄 动态调整: 根据市场波动性调整信号识别阈值")
        
        return "\n".join(conclusions)
    
    def run_comprehensive_analysis(self):
        """运行综合分析"""
        print("开始生成综合分析报告...")
        
        # 获取分析结果
        all_results = self.monitor.run_sensitive_analysis(period='5m', limit=500)
        
        # 生成总结图表
        summary_fig = self.generate_summary_chart(all_results)
        summary_fig.savefig('主力资金监测总结仪表板.png', dpi=300, bbox_inches='tight')
        plt.close(summary_fig)
        
        # 生成详细结论
        detailed_conclusion = self.generate_detailed_conclusion(all_results)
        
        # 保存结论报告
        with open('主力资金监测综合结论报告.txt', 'w', encoding='utf-8') as f:
            f.write(detailed_conclusion)
        
        print("综合分析报告生成完成！")
        print("- 总结仪表板: 主力资金监测总结仪表板.png")
        print("- 详细报告: 主力资金监测综合结论报告.txt")
        
        return all_results, detailed_conclusion


def main():
    """主函数"""
    generator = ComprehensiveReportGenerator()
    results, conclusion = generator.run_comprehensive_analysis()
    
    print("\n" + "=" * 60)
    print("主力资金监测结论:")
    print("=" * 60)
    print(conclusion)


if __name__ == "__main__":
    main()
