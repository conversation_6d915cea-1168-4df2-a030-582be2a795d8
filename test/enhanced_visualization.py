#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版主力资金监测可视化
创建界面优美的分析图表
"""

import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
import numpy as np
from datetime import datetime
import matplotlib.dates as mdates
import matplotlib.patches as patches

# 设置中文字体和样式
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
sns.set_style("whitegrid")
sns.set_palette("husl")

class EnhancedVisualization:
    """增强版可视化类"""
    
    def __init__(self):
        self.colors = {
            '主力做多建仓': '#00C851',  # 绿色
            '主力做空建仓': '#FF4444',  # 红色
            '主力平多仓': '#FF8800',    # 橙色
            '主力平空仓': '#0099CC',    # 蓝色
        }
        
        self.markers = {
            '主力做多建仓': '^',  # 向上三角
            '主力做空建仓': 'v',  # 向下三角
            '主力平多仓': 's',    # 方形
            '主力平空仓': 'o',    # 圆形
        }
    
    def create_comprehensive_chart(self, symbol, merged_data, signals_df):
        """创建综合分析图表"""
        fig = plt.figure(figsize=(16, 12))
        gs = fig.add_gridspec(4, 2, height_ratios=[2, 1, 1, 1], width_ratios=[3, 1])
        
        # 主图：价格 + 信号
        ax_main = fig.add_subplot(gs[0, 0])
        self._plot_price_with_signals(ax_main, merged_data, signals_df, symbol)
        
        # 信号统计饼图
        ax_pie = fig.add_subplot(gs[0, 1])
        self._plot_signal_pie_chart(ax_pie, signals_df)
        
        # 持仓量图
        ax_oi = fig.add_subplot(gs[1, :])
        self._plot_open_interest(ax_oi, merged_data, signals_df)
        
        # 多空比图
        ax_ls = fig.add_subplot(gs[2, :])
        self._plot_long_short_ratio(ax_ls, merged_data, signals_df)
        
        # 信号时间线
        ax_timeline = fig.add_subplot(gs[3, :])
        self._plot_signal_timeline(ax_timeline, signals_df)
        
        plt.suptitle(f'{symbol} 主力资金监测综合分析', fontsize=18, fontweight='bold', y=0.98)
        plt.tight_layout()
        
        return fig
    
    def _plot_price_with_signals(self, ax, merged_data, signals_df, symbol):
        """绘制价格走势和信号"""
        # 绘制价格线
        ax.plot(merged_data['timestamp'], merged_data['close'], 
                color='#2E86AB', linewidth=2, label='价格走势', alpha=0.8)
        
        # 添加信号点
        if not signals_df.empty:
            for signal_type in signals_df['signal_type'].unique():
                signal_data = signals_df[signals_df['signal_type'] == signal_type]
                ax.scatter(signal_data['timestamp'], signal_data['price'],
                          color=self.colors.get(signal_type, 'gray'),
                          marker=self.markers.get(signal_type, 'o'),
                          s=120, alpha=0.9, edgecolors='white', linewidth=2,
                          label=signal_type, zorder=5)
        
        ax.set_title(f'{symbol} 价格走势与主力资金信号', fontsize=14, fontweight='bold', pad=20)
        ax.set_ylabel('价格 (USDT)', fontsize=12)
        ax.grid(True, alpha=0.3)
        ax.legend(loc='upper left', frameon=True, fancybox=True, shadow=True)
        
        # 格式化x轴
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d %H:%M'))
        ax.xaxis.set_major_locator(mdates.HourLocator(interval=6))
        plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
    
    def _plot_signal_pie_chart(self, ax, signals_df):
        """绘制信号统计饼图"""
        if signals_df.empty:
            ax.text(0.5, 0.5, '暂无信号', ha='center', va='center', 
                   fontsize=14, transform=ax.transAxes)
            ax.set_title('信号分布', fontsize=12, fontweight='bold')
            return
        
        signal_counts = signals_df['signal_type'].value_counts()
        colors = [self.colors.get(signal, 'gray') for signal in signal_counts.index]
        
        wedges, texts, autotexts = ax.pie(signal_counts.values, 
                                         labels=signal_counts.index,
                                         colors=colors,
                                         autopct='%1.1f%%',
                                         startangle=90,
                                         textprops={'fontsize': 10})
        
        # 美化文本
        for autotext in autotexts:
            autotext.set_color('white')
            autotext.set_fontweight('bold')
        
        ax.set_title('主力资金信号分布', fontsize=12, fontweight='bold')
    
    def _plot_open_interest(self, ax, merged_data, signals_df):
        """绘制持仓量"""
        ax.fill_between(merged_data['timestamp'], merged_data['sumOpenInterest'],
                       alpha=0.3, color='#A8E6CF', label='持仓量')
        ax.plot(merged_data['timestamp'], merged_data['sumOpenInterest'],
                color='#4CAF50', linewidth=2)
        
        # 标记信号点
        if not signals_df.empty:
            for _, signal in signals_df.iterrows():
                ax.axvline(x=signal['timestamp'], color='red', linestyle='--', alpha=0.5)
        
        ax.set_title('持仓量变化', fontsize=12, fontweight='bold')
        ax.set_ylabel('持仓量', fontsize=10)
        ax.grid(True, alpha=0.3)
        ax.legend()
    
    def _plot_long_short_ratio(self, ax, merged_data, signals_df):
        """绘制多空比"""
        ax.plot(merged_data['timestamp'], merged_data['longShortRatio'],
                color='#FF6B6B', linewidth=2, label='多空比')
        ax.axhline(y=1, color='gray', linestyle='-', alpha=0.5, label='平衡线')
        
        # 填充区域
        ax.fill_between(merged_data['timestamp'], merged_data['longShortRatio'], 1,
                       where=(merged_data['longShortRatio'] > 1),
                       alpha=0.3, color='green', label='多头占优')
        ax.fill_between(merged_data['timestamp'], merged_data['longShortRatio'], 1,
                       where=(merged_data['longShortRatio'] < 1),
                       alpha=0.3, color='red', label='空头占优')
        
        # 标记信号点
        if not signals_df.empty:
            for _, signal in signals_df.iterrows():
                ax.axvline(x=signal['timestamp'], color='red', linestyle='--', alpha=0.5)
        
        ax.set_title('多空持仓人数比', fontsize=12, fontweight='bold')
        ax.set_ylabel('多空比', fontsize=10)
        ax.grid(True, alpha=0.3)
        ax.legend()
    
    def _plot_signal_timeline(self, ax, signals_df):
        """绘制信号时间线"""
        if signals_df.empty:
            ax.text(0.5, 0.5, '暂无信号数据', ha='center', va='center',
                   fontsize=12, transform=ax.transAxes)
            ax.set_title('信号时间线', fontsize=12, fontweight='bold')
            return
        
        # 创建时间线
        y_positions = {signal_type: i for i, signal_type in enumerate(signals_df['signal_type'].unique())}
        
        for _, signal in signals_df.iterrows():
            y_pos = y_positions[signal['signal_type']]
            color = self.colors.get(signal['signal_type'], 'gray')
            marker = self.markers.get(signal['signal_type'], 'o')
            
            ax.scatter(signal['timestamp'], y_pos, 
                      color=color, marker=marker, s=100, alpha=0.8,
                      edgecolors='white', linewidth=1)
        
        ax.set_yticks(list(y_positions.values()))
        ax.set_yticklabels(list(y_positions.keys()))
        ax.set_title('主力资金信号时间线', fontsize=12, fontweight='bold')
        ax.grid(True, alpha=0.3)
        
        # 格式化x轴
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d %H:%M'))
        plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
    
    def create_summary_dashboard(self, all_results):
        """创建总结仪表板"""
        fig, axes = plt.subplots(2, 2, figsize=(16, 10))
        fig.suptitle('主力资金监测总结仪表板', fontsize=18, fontweight='bold')
        
        symbols = list(all_results.keys())
        
        for i, symbol in enumerate(symbols[:4]):  # 最多显示4个币种
            row, col = i // 2, i % 2
            ax = axes[row, col]
            
            if symbol in all_results:
                signals_df = all_results[symbol]['signals']
                merged_data = all_results[symbol]['merged_data']
                
                # 绘制简化的价格和信号
                ax.plot(merged_data['timestamp'], merged_data['close'], 
                       color='blue', linewidth=1.5, alpha=0.7)
                
                if not signals_df.empty:
                    for signal_type in signals_df['signal_type'].unique():
                        signal_data = signals_df[signals_df['signal_type'] == signal_type]
                        ax.scatter(signal_data['timestamp'], signal_data['price'],
                                  color=self.colors.get(signal_type, 'gray'),
                                  marker=self.markers.get(signal_type, 'o'),
                                  s=60, alpha=0.8)
                
                ax.set_title(f'{symbol} (信号数: {len(signals_df)})', fontsize=12)
                ax.grid(True, alpha=0.3)
                
                # 格式化x轴
                ax.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d'))
                plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
        
        plt.tight_layout()
        return fig
