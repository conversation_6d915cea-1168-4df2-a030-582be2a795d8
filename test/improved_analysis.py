#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进版主力资金监测分析
修复时区问题，优化置信度计算
"""

import sys
import os
sys.path.append('..')

from main_fund_monitor import MainFundMonitor
import matplotlib.pyplot as plt
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import pytz

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class ImprovedMainFundMonitor(MainFundMonitor):
    """改进版主力资金监测器"""
    
    def __init__(self):
        super().__init__()
        self.beijing_tz = pytz.timezone('Asia/Shanghai')
        
    def convert_to_beijing_time(self, timestamp):
        """将UTC时间戳转换为北京时间"""
        if isinstance(timestamp, (int, float)):
            utc_time = pd.to_datetime(timestamp, unit='ms')
        else:
            utc_time = pd.to_datetime(timestamp)
        
        # 如果时间戳没有时区信息，假设为UTC
        if utc_time.tz is None:
            utc_time = utc_time.tz_localize('UTC')
        
        beijing_time = utc_time.tz_convert('Asia/Shanghai')
        return beijing_time
    
    def format_beijing_time(self, timestamp):
        """格式化北京时间显示"""
        beijing_time = self.convert_to_beijing_time(timestamp)
        return beijing_time.strftime('%m-%d %H:%M (UTC+8)')
    
    def calculate_improved_confidence(self, oi_change, ls_change, oi_threshold, ls_threshold):
        """改进的置信度计算方法"""
        # 标准化变化值
        oi_score = abs(oi_change) / oi_threshold
        ls_score = abs(ls_change) / ls_threshold
        
        # 使用几何平均数，避免单一指标过度影响
        base_confidence = (oi_score * ls_score) ** 0.5
        
        # 信号一致性检查
        consistency_bonus = 1.0
        
        # 检查信号方向一致性
        if (oi_change > 0 and ls_change < 0) or (oi_change > 0 and ls_change > 0):
            # 持仓上升的情况
            consistency_bonus = 1.2
        elif (oi_change < 0 and ls_change > 0) or (oi_change < 0 and ls_change < 0):
            # 持仓下降的情况  
            consistency_bonus = 1.2
        
        # 强度奖励：如果两个指标都很强
        if oi_score > 2.0 and ls_score > 2.0:
            consistency_bonus *= 1.3
        
        final_confidence = base_confidence * consistency_bonus
        
        # 限制最大置信度为2.0
        return min(final_confidence, 2.0)
    
    def get_confidence_level(self, confidence):
        """获取置信度等级"""
        if confidence >= 1.0:
            return "🟢 高置信度"
        elif confidence >= 0.6:
            return "🟡 中等置信度"
        elif confidence >= 0.3:
            return "🟠 低置信度"
        else:
            return "🔴 极低置信度"
    
    def analyze_main_fund_signals(self, oi_data, ls_data, price_data):
        """分析主力资金信号 - 改进版本"""
        # 转换所有时间戳为北京时间
        oi_data = oi_data.copy()
        ls_data = ls_data.copy()
        price_data = price_data.copy()
        
        oi_data['timestamp'] = oi_data['timestamp'].apply(self.convert_to_beijing_time)
        ls_data['timestamp'] = ls_data['timestamp'].apply(self.convert_to_beijing_time)
        price_data['timestamp'] = price_data['timestamp'].apply(self.convert_to_beijing_time)
        
        # 合并数据
        merged_data = pd.merge(oi_data, ls_data, on='timestamp', how='inner')
        merged_data = pd.merge(merged_data, price_data[['timestamp', 'close']], on='timestamp', how='inner')
        
        # 计算变化率
        merged_data['oi_change_pct'] = merged_data['sumOpenInterest'].pct_change() * 100
        merged_data['ls_ratio_change_pct'] = merged_data['longShortRatio'].pct_change() * 100
        merged_data['price_change_pct'] = merged_data['close'].pct_change() * 100
        
        # 使用敏感的阈值
        oi_threshold = 0.5  # 持仓量变化阈值
        ls_threshold = 2.0  # 多空比变化阈值
        
        # 识别主力资金信号
        signals = []
        
        for i in range(1, len(merged_data)):
            row = merged_data.iloc[i]
            
            oi_change = row['oi_change_pct']
            ls_change = row['ls_ratio_change_pct']
            
            signal_type = None
            signal_desc = None
            
            # 根据理论分析信号
            if abs(oi_change) > oi_threshold:
                if oi_change > 0:  # 持仓上升
                    if ls_change < -ls_threshold:  # 多空比下降
                        signal_type = "主力做多建仓"
                        signal_desc = f"持仓上升{oi_change:.2f}%+多空比下降{ls_change:.2f}%，主力大额做多"
                    elif ls_change > ls_threshold:  # 多空比上升
                        signal_type = "主力做空建仓"
                        signal_desc = f"持仓上升{oi_change:.2f}%+多空比上升{ls_change:.2f}%，主力大额做空"
                else:  # 持仓下降
                    if ls_change > ls_threshold:  # 多空比上升
                        signal_type = "主力平多仓"
                        signal_desc = f"持仓下降{oi_change:.2f}%+多空比上升{ls_change:.2f}%，主力平多单"
                    elif ls_change < -ls_threshold:  # 多空比下降
                        signal_type = "主力平空仓"
                        signal_desc = f"持仓下降{oi_change:.2f}%+多空比下降{ls_change:.2f}%，主力平空单"
            
            if signal_type:
                # 使用改进的置信度计算
                confidence = self.calculate_improved_confidence(oi_change, ls_change, oi_threshold, ls_threshold)
                confidence_level = self.get_confidence_level(confidence)
                
                signals.append({
                    'timestamp': row['timestamp'],
                    'timestamp_str': self.format_beijing_time(row['timestamp']),
                    'signal_type': signal_type,
                    'signal_desc': signal_desc,
                    'confidence': confidence,
                    'confidence_level': confidence_level,
                    'oi_change_pct': oi_change,
                    'ls_change_pct': ls_change,
                    'price_change_pct': row['price_change_pct'],
                    'price': row['close'],
                    'open_interest': row['sumOpenInterest'],
                    'long_short_ratio': row['longShortRatio']
                })
        
        return pd.DataFrame(signals), merged_data
    
    def create_improved_chart(self, symbol, merged_data, signals_df):
        """创建改进版分析图表"""
        fig, axes = plt.subplots(5, 1, figsize=(16, 14))
        fig.suptitle(f'{symbol} 主力资金监测分析 (时区: UTC+8)', fontsize=16, fontweight='bold')
        
        # 价格走势
        axes[0].plot(merged_data['timestamp'], merged_data['close'], 'b-', linewidth=2, label='价格')
        
        # 添加信号标记
        if not signals_df.empty:
            colors = {'主力做多建仓': 'green', '主力做空建仓': 'red', 
                     '主力平多仓': 'orange', '主力平空仓': 'blue'}
            markers = {'主力做多建仓': '^', '主力做空建仓': 'v', 
                      '主力平多仓': 's', '主力平空仓': 'o'}
            
            for _, signal in signals_df.iterrows():
                color = colors.get(signal['signal_type'], 'gray')
                marker = markers.get(signal['signal_type'], 'o')
                
                # 根据置信度调整标记大小
                size = 50 + signal['confidence'] * 100
                
                axes[0].scatter(signal['timestamp'], signal['price'],
                              color=color, marker=marker, s=size, alpha=0.8, 
                              label=f"{signal['signal_type']} ({signal['confidence']:.2f})", 
                              zorder=5, edgecolors='white', linewidth=2)
        
        axes[0].set_title('价格走势与主力资金信号 (标记大小表示置信度)', fontsize=12)
        axes[0].set_ylabel('价格 (USDT)', fontsize=10)
        axes[0].grid(True, alpha=0.3)
        axes[0].legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        
        # 持仓量
        axes[1].plot(merged_data['timestamp'], merged_data['sumOpenInterest'], 'g-', linewidth=2)
        axes[1].fill_between(merged_data['timestamp'], merged_data['sumOpenInterest'], alpha=0.3, color='green')
        axes[1].set_title('持仓量变化', fontsize=12)
        axes[1].set_ylabel('持仓量', fontsize=10)
        axes[1].grid(True, alpha=0.3)
        
        # 多空比
        axes[2].plot(merged_data['timestamp'], merged_data['longShortRatio'], 'r-', linewidth=2)
        axes[2].axhline(y=1, color='gray', linestyle='--', alpha=0.5, label='平衡线')
        axes[2].fill_between(merged_data['timestamp'], merged_data['longShortRatio'], 1,
                           where=(merged_data['longShortRatio'] > 1), alpha=0.3, color='green', label='多头占优')
        axes[2].fill_between(merged_data['timestamp'], merged_data['longShortRatio'], 1,
                           where=(merged_data['longShortRatio'] < 1), alpha=0.3, color='red', label='空头占优')
        axes[2].set_title('多空持仓人数比', fontsize=12)
        axes[2].set_ylabel('多空比', fontsize=10)
        axes[2].grid(True, alpha=0.3)
        axes[2].legend()
        
        # 持仓量变化率
        axes[3].bar(merged_data['timestamp'], merged_data['oi_change_pct'], 
                   color=['green' if x > 0 else 'red' for x in merged_data['oi_change_pct']], alpha=0.7)
        axes[3].axhline(y=0, color='black', linestyle='-', alpha=0.5)
        axes[3].axhline(y=0.5, color='green', linestyle='--', alpha=0.5, label='阈值 +0.5%')
        axes[3].axhline(y=-0.5, color='red', linestyle='--', alpha=0.5, label='阈值 -0.5%')
        axes[3].set_title('持仓量变化率', fontsize=12)
        axes[3].set_ylabel('变化率 (%)', fontsize=10)
        axes[3].grid(True, alpha=0.3)
        axes[3].legend()
        
        # 多空比变化率
        axes[4].bar(merged_data['timestamp'], merged_data['ls_ratio_change_pct'], 
                   color=['blue' if x > 0 else 'purple' for x in merged_data['ls_ratio_change_pct']], alpha=0.7)
        axes[4].axhline(y=0, color='black', linestyle='-', alpha=0.5)
        axes[4].axhline(y=2.0, color='blue', linestyle='--', alpha=0.5, label='阈值 +2.0%')
        axes[4].axhline(y=-2.0, color='purple', linestyle='--', alpha=0.5, label='阈值 -2.0%')
        axes[4].set_title('多空比变化率', fontsize=12)
        axes[4].set_ylabel('变化率 (%)', fontsize=10)
        axes[4].grid(True, alpha=0.3)
        axes[4].legend()
        
        # 格式化x轴时间显示
        for ax in axes:
            ax.tick_params(axis='x', rotation=45)
        
        plt.tight_layout()
        return fig
    
    def run_improved_analysis(self, period='5m', limit=500):
        """运行改进版分析"""
        print("开始改进版主力资金监测分析...")
        print("时区: UTC+8 (北京时间)")
        print("=" * 50)
        
        all_results = {}
        
        for symbol in self.symbols:
            print(f"\n正在分析 {symbol}...")
            
            # 获取数据
            kline_data = self.get_kline_data(symbol, period, limit)
            oi_data = self.get_open_interest_data(symbol, period, limit)
            ls_data = self.get_long_short_ratio_data(symbol, period, limit)
            
            if kline_data is None or oi_data is None or ls_data is None:
                print(f"  {symbol} 数据获取失败，跳过分析")
                continue
            
            # 分析信号
            signals_df, merged_data = self.analyze_main_fund_signals(oi_data, ls_data, kline_data)
            
            # 保存结果
            all_results[symbol] = {
                'signals': signals_df,
                'merged_data': merged_data
            }
            
            # 创建改进版图表
            fig = self.create_improved_chart(symbol, merged_data, signals_df)
            fig.savefig(f'{symbol}_改进版主力资金分析.png', dpi=300, bbox_inches='tight')
            plt.close(fig)
            
            print(f"  {symbol} 分析完成，发现 {len(signals_df)} 个主力资金信号")
            
            # 显示详细信号信息
            if not signals_df.empty:
                print("  详细信号信息:")
                for _, signal in signals_df.iterrows():
                    print(f"    {signal['timestamp_str']} - {signal['signal_type']}")
                    print(f"      置信度: {signal['confidence']:.3f} ({signal['confidence_level']})")
                    print(f"      持仓变化: {signal['oi_change_pct']:.2f}%, 多空比变化: {signal['ls_change_pct']:.2f}%")
                    print()
        
        return all_results


def main():
    """主函数"""
    monitor = ImprovedMainFundMonitor()
    results = monitor.run_improved_analysis(period='5m', limit=500)
    
    print("\n" + "=" * 50)
    print("改进版分析完成！")
    print("- 修复了时区问题，统一使用UTC+8显示")
    print("- 优化了置信度计算方法")
    print("- 增加了置信度等级显示")
    print("- 图表已保存到当前目录")


if __name__ == "__main__":
    main()
