#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
敏感版主力资金监测分析
使用更低的阈值来捕获更多信号
"""

import sys
import os
sys.path.append('..')

from main_fund_monitor import MainFundMonitor
import matplotlib.pyplot as plt
import pandas as pd
import numpy as np

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class SensitiveMainFundMonitor(MainFundMonitor):
    """敏感版主力资金监测器"""
    
    def analyze_main_fund_signals(self, oi_data, ls_data, price_data):
        """分析主力资金信号 - 敏感版本"""
        # 合并数据
        merged_data = pd.merge(oi_data, ls_data, on='timestamp', how='inner')
        merged_data = pd.merge(merged_data, price_data[['timestamp', 'close']], on='timestamp', how='inner')
        
        # 计算变化率
        merged_data['oi_change_pct'] = merged_data['sumOpenInterest'].pct_change() * 100
        merged_data['ls_ratio_change_pct'] = merged_data['longShortRatio'].pct_change() * 100
        merged_data['price_change_pct'] = merged_data['close'].pct_change() * 100
        
        # 使用更敏感的阈值
        oi_threshold = 0.5  # 降低持仓量变化阈值
        ls_threshold = 2.0  # 降低多空比变化阈值
        
        # 识别主力资金信号
        signals = []
        
        for i in range(1, len(merged_data)):
            row = merged_data.iloc[i]
            
            oi_change = row['oi_change_pct']
            ls_change = row['ls_ratio_change_pct']
            
            signal_type = None
            signal_desc = None
            confidence = 0
            
            # 根据理论分析信号
            if abs(oi_change) > oi_threshold:
                if oi_change > 0:  # 持仓上升
                    if ls_change < -ls_threshold:  # 多空比下降
                        signal_type = "主力做多建仓"
                        signal_desc = f"持仓上升{oi_change:.2f}%+多空比下降{ls_change:.2f}%，主力大额做多"
                        confidence = min(abs(oi_change), abs(ls_change)) / max(oi_threshold, ls_threshold)
                    elif ls_change > ls_threshold:  # 多空比上升
                        signal_type = "主力做空建仓"
                        signal_desc = f"持仓上升{oi_change:.2f}%+多空比上升{ls_change:.2f}%，主力大额做空"
                        confidence = min(abs(oi_change), abs(ls_change)) / max(oi_threshold, ls_threshold)
                else:  # 持仓下降
                    if ls_change > ls_threshold:  # 多空比上升
                        signal_type = "主力平多仓"
                        signal_desc = f"持仓下降{oi_change:.2f}%+多空比上升{ls_change:.2f}%，主力平多单"
                        confidence = min(abs(oi_change), abs(ls_change)) / max(oi_threshold, ls_threshold)
                    elif ls_change < -ls_threshold:  # 多空比下降
                        signal_type = "主力平空仓"
                        signal_desc = f"持仓下降{oi_change:.2f}%+多空比下降{ls_change:.2f}%，主力平空单"
                        confidence = min(abs(oi_change), abs(ls_change)) / max(oi_threshold, ls_threshold)
            
            if signal_type:
                signals.append({
                    'timestamp': row['timestamp'],
                    'signal_type': signal_type,
                    'signal_desc': signal_desc,
                    'confidence': confidence,
                    'oi_change_pct': oi_change,
                    'ls_change_pct': ls_change,
                    'price_change_pct': row['price_change_pct'],
                    'price': row['close'],
                    'open_interest': row['sumOpenInterest'],
                    'long_short_ratio': row['longShortRatio']
                })
        
        return pd.DataFrame(signals), merged_data
    
    def create_detailed_analysis_chart(self, symbol, merged_data, signals_df):
        """创建详细分析图表"""
        fig, axes = plt.subplots(5, 1, figsize=(16, 14))
        fig.suptitle(f'{symbol} 主力资金详细监测分析', fontsize=16, fontweight='bold')
        
        # 价格走势
        axes[0].plot(merged_data['timestamp'], merged_data['close'], 'b-', linewidth=2, label='价格')
        
        # 添加信号标记
        if not signals_df.empty:
            colors = {'主力做多建仓': 'green', '主力做空建仓': 'red', 
                     '主力平多仓': 'orange', '主力平空仓': 'blue'}
            markers = {'主力做多建仓': '^', '主力做空建仓': 'v', 
                      '主力平多仓': 's', '主力平空仓': 'o'}
            
            for signal_type in signals_df['signal_type'].unique():
                signal_data = signals_df[signals_df['signal_type'] == signal_type]
                axes[0].scatter(signal_data['timestamp'], signal_data['price'],
                              color=colors.get(signal_type, 'gray'),
                              marker=markers.get(signal_type, 'o'),
                              s=100, alpha=0.8, label=signal_type, zorder=5)
        
        axes[0].set_title('价格走势与主力资金信号', fontsize=12)
        axes[0].set_ylabel('价格 (USDT)', fontsize=10)
        axes[0].grid(True, alpha=0.3)
        axes[0].legend()
        
        # 持仓量
        axes[1].plot(merged_data['timestamp'], merged_data['sumOpenInterest'], 'g-', linewidth=2)
        axes[1].fill_between(merged_data['timestamp'], merged_data['sumOpenInterest'], alpha=0.3, color='green')
        axes[1].set_title('持仓量变化', fontsize=12)
        axes[1].set_ylabel('持仓量', fontsize=10)
        axes[1].grid(True, alpha=0.3)
        
        # 多空比
        axes[2].plot(merged_data['timestamp'], merged_data['longShortRatio'], 'r-', linewidth=2)
        axes[2].axhline(y=1, color='gray', linestyle='--', alpha=0.5, label='平衡线')
        axes[2].fill_between(merged_data['timestamp'], merged_data['longShortRatio'], 1,
                           where=(merged_data['longShortRatio'] > 1), alpha=0.3, color='green', label='多头占优')
        axes[2].fill_between(merged_data['timestamp'], merged_data['longShortRatio'], 1,
                           where=(merged_data['longShortRatio'] < 1), alpha=0.3, color='red', label='空头占优')
        axes[2].set_title('多空持仓人数比', fontsize=12)
        axes[2].set_ylabel('多空比', fontsize=10)
        axes[2].grid(True, alpha=0.3)
        axes[2].legend()
        
        # 持仓量变化率
        axes[3].bar(merged_data['timestamp'], merged_data['oi_change_pct'], 
                   color=['green' if x > 0 else 'red' for x in merged_data['oi_change_pct']], alpha=0.7)
        axes[3].axhline(y=0, color='black', linestyle='-', alpha=0.5)
        axes[3].set_title('持仓量变化率', fontsize=12)
        axes[3].set_ylabel('变化率 (%)', fontsize=10)
        axes[3].grid(True, alpha=0.3)
        
        # 多空比变化率
        axes[4].bar(merged_data['timestamp'], merged_data['ls_ratio_change_pct'], 
                   color=['blue' if x > 0 else 'purple' for x in merged_data['ls_ratio_change_pct']], alpha=0.7)
        axes[4].axhline(y=0, color='black', linestyle='-', alpha=0.5)
        axes[4].set_title('多空比变化率', fontsize=12)
        axes[4].set_ylabel('变化率 (%)', fontsize=10)
        axes[4].grid(True, alpha=0.3)
        
        plt.tight_layout()
        return fig
    
    def run_sensitive_analysis(self, period='5m', limit=500):
        """运行敏感分析"""
        print("开始敏感版主力资金监测分析...")
        print("=" * 50)
        
        all_results = {}
        
        for symbol in self.symbols:
            print(f"\n正在分析 {symbol}...")
            
            # 获取数据
            kline_data = self.get_kline_data(symbol, period, limit)
            oi_data = self.get_open_interest_data(symbol, period, limit)
            ls_data = self.get_long_short_ratio_data(symbol, period, limit)
            
            if kline_data is None or oi_data is None or ls_data is None:
                print(f"  {symbol} 数据获取失败，跳过分析")
                continue
            
            # 分析信号
            signals_df, merged_data = self.analyze_main_fund_signals(oi_data, ls_data, kline_data)
            
            # 保存结果
            all_results[symbol] = {
                'signals': signals_df,
                'merged_data': merged_data
            }
            
            # 创建详细图表
            fig = self.create_detailed_analysis_chart(symbol, merged_data, signals_df)
            fig.savefig(f'{symbol}_敏感版主力资金分析.png', dpi=300, bbox_inches='tight')
            plt.close(fig)
            
            print(f"  {symbol} 分析完成，发现 {len(signals_df)} 个主力资金信号")
            
            # 显示最近的信号
            if not signals_df.empty:
                print("  最近的信号:")
                for _, signal in signals_df.tail(3).iterrows():
                    print(f"    {signal['timestamp'].strftime('%m-%d %H:%M')} - {signal['signal_type']} (置信度: {signal['confidence']:.2f})")
        
        return all_results


def main():
    """主函数"""
    monitor = SensitiveMainFundMonitor()
    results = monitor.run_sensitive_analysis(period='5m', limit=500)
    
    print("\n" + "=" * 50)
    print("敏感版分析完成！")
    print("详细图表已保存到当前目录")


if __name__ == "__main__":
    main()
