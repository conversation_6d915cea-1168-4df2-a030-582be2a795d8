# 主力资金监测Web系统

## 🎯 系统概述

基于Web的主力资金监测查询系统，集成了我们之前开发的主力资金监测分析功能，提供直观的Web界面进行永续合约的主力资金分析。

## ✨ 主要功能

### 📊 核心分析功能
- **主力资金信号识别**: 基于持仓量(OI)和多空比的综合分析
- **实时数据获取**: 调用币安API获取最新的市场数据
- **智能置信度计算**: 改进版置信度算法，更准确评估信号可靠性
- **时区统一处理**: 所有时间统一显示为UTC+8（北京时间）

### 🖥️ Web界面特性
- **响应式设计**: 支持桌面和移动设备
- **现代化UI**: 使用Bootstrap 5和自定义CSS样式
- **实时图表**: 集成matplotlib生成的高质量分析图表
- **缓存优化**: 内存缓存机制，提升查询性能

### 📈 支持的合约
支持24个永续合约，包括：
- **主流币种**: BTCUSDT, ETHUSDT (排在前两位)
- **热门山寨币**: DOGEUSDT, ADAUSDT, SOLUSDT, AVAXUSDT等
- **新兴代币**: TRUMPUSDT, MELANIAUSDT等

### ⏰ 时间周期
支持9个时间周期：`5m`, `15m`, `30m`, `1h`, `2h`, `4h`, `6h`, `12h`, `1d`

## 🚀 快速启动

### 1. 环境要求
- Python 3.8+
- 网络连接（访问币安API）

### 2. 安装依赖
```bash
pip install -r requirements.txt
```

### 3. 启动方式

#### 方式一：使用启动脚本（推荐）
```bash
python start_web_app.py
```

#### 方式二：直接启动
```bash
python web_app.py
```

### 4. 访问系统
打开浏览器访问：`http://localhost:5001`

## 📱 使用指南

### 界面操作
1. **选择合约**: 从下拉框中选择要分析的永续合约
2. **选择周期**: 选择分析的时间周期（默认5分钟）
3. **开始分析**: 点击"开始分析"按钮
4. **查看结果**: 系统将显示分析图表和详细报告

### 功能说明

#### 🔍 分析结果包含：
- **统计概览**: 信号数量、类型、合约信息等
- **分析图表**: 价格走势、持仓量变化、多空比变化
- **详细报告**: 分析结论、信号统计、投资建议
- **最近信号**: 按时间排序的信号列表，包含置信度

#### 📊 信号类型：
1. **主力做多建仓**: 持仓上升 + 多空比下降
2. **主力做空建仓**: 持仓上升 + 多空比上升  
3. **主力平多仓**: 持仓下降 + 多空比上升
4. **主力平空仓**: 持仓下降 + 多空比下降

#### 🎯 置信度等级：
- 🟢 **高置信度** (≥1.0): 信号非常可靠
- 🟡 **中等置信度** (0.6-1.0): 信号较为可靠
- 🟠 **低置信度** (0.3-0.6): 需要谨慎对待
- 🔴 **极低置信度** (<0.3): 可能是噪音信号

## 🔧 技术架构

### 后端技术栈
- **Web框架**: Flask + Flask-CORS
- **数据处理**: Pandas + NumPy
- **图表生成**: Matplotlib
- **缓存系统**: CacheTools (TTL缓存)
- **时区处理**: PyTZ
- **API调用**: Requests

### 前端技术栈
- **UI框架**: Bootstrap 5
- **图标库**: Font Awesome 6
- **JavaScript**: 原生ES6+
- **样式**: 自定义CSS + 渐变设计

### 数据源
- **K线数据**: `https://fapi.binance.com/fapi/v1/klines`
- **多空比**: `https://fapi.binance.com/futures/data/globalLongShortAccountRatio`
- **持仓量**: `https://fapi.binance.com/futures/data/openInterestHist`

## ⚡ 性能优化

### 缓存机制
- **TTL缓存**: 5分钟自动过期
- **内存存储**: 不使用持久化数据库
- **自动清理**: 过期数据自动释放内存
- **手动清除**: 支持一键清除缓存

### 资源管理
- **图表优化**: 使用Agg后端，减少内存占用
- **数据限制**: 合理限制API调用数据量
- **错误处理**: 完善的异常处理和用户提示

## 🛠️ API接口

### 分析接口
```
POST /api/analyze
Content-Type: application/json

{
    "symbol": "BTCUSDT",
    "period": "5m"
}
```

### 缓存管理
```
GET /api/cache/status    # 查看缓存状态
POST /api/cache/clear    # 清除缓存
```

## 🧪 测试验证

运行测试脚本验证系统功能：
```bash
python test_web_system.py
```

测试包含：
- ✅ 模块导入测试
- ✅ 监测器功能测试  
- ✅ 图表生成测试
- ✅ Web API测试

## ⚠️ 注意事项

### 使用建议
1. **网络要求**: 需要稳定的网络连接访问币安API
2. **数据延迟**: API数据可能有1-2分钟延迟
3. **缓存策略**: 相同查询5分钟内返回缓存结果
4. **资源占用**: 图表生成会消耗一定CPU和内存

### 风险提示
1. **投资风险**: 分析结果仅供参考，不构成投资建议
2. **信号验证**: 建议结合其他技术指标进行确认
3. **风险控制**: 任何交易都应设置止损和仓位管理
4. **市场变化**: 主力资金不一定总是正确的

## 🔄 系统维护

### 日志监控
系统运行日志会显示：
- API调用状态
- 缓存命中情况
- 错误信息记录

### 故障排除
1. **端口占用**: 修改web_app.py中的端口号
2. **依赖缺失**: 运行`pip install -r requirements.txt`
3. **API失败**: 检查网络连接和币安API状态
4. **内存不足**: 清除缓存或重启服务

## 📞 技术支持

如遇到问题，请检查：
1. Python版本是否符合要求
2. 依赖包是否正确安装
3. 网络连接是否正常
4. 系统日志中的错误信息

---

**版本**: 1.0.0  
**更新时间**: 2025-07-24  
**兼容性**: Python 3.8+ / 现代浏览器
