#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主力资金监测系统
基于持仓量(OI)和多空比的综合分析
"""

import requests
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import warnings
import os
import matplotlib.font_manager as fm

warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class MainFundMonitor:
    """主力资金监测器"""
    
    def __init__(self):
        self.base_url = "https://fapi.binance.com"
        self.symbols = ['BTCUSDT', 'ETHUSDT', 'XRPUSDT']
        self.periods = ['5m', '15m', '1h', '4h', '1d']
        
    def get_kline_data(self, symbol, interval='5m', limit=500):
        """获取K线数据"""
        url = f"{self.base_url}/fapi/v1/klines"
        params = {
            'symbol': symbol,
            'interval': interval,
            'limit': limit
        }
        
        try:
            response = requests.get(url, params=params)
            response.raise_for_status()
            data = response.json()
            
            df = pd.DataFrame(data, columns=[
                'timestamp', 'open', 'high', 'low', 'close', 'volume',
                'close_time', 'quote_volume', 'count', 'taker_buy_volume',
                'taker_buy_quote_volume', 'ignore'
            ])
            
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            df['close'] = df['close'].astype(float)
            df['volume'] = df['volume'].astype(float)
            
            return df
            
        except Exception as e:
            print(f"获取K线数据失败 {symbol}: {e}")
            return None
    
    def get_open_interest_data(self, symbol, period='5m', limit=500):
        """获取持仓量数据"""
        url = f"{self.base_url}/futures/data/openInterestHist"
        params = {
            'symbol': symbol,
            'period': period,
            'limit': limit
        }
        
        try:
            response = requests.get(url, params=params)
            response.raise_for_status()
            data = response.json()
            
            df = pd.DataFrame(data)
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            df['sumOpenInterest'] = df['sumOpenInterest'].astype(float)
            df['sumOpenInterestValue'] = df['sumOpenInterestValue'].astype(float)
            
            return df
            
        except Exception as e:
            print(f"获取持仓量数据失败 {symbol}: {e}")
            return None
    
    def get_long_short_ratio_data(self, symbol, period='5m', limit=500):
        """获取多空比数据"""
        url = f"{self.base_url}/futures/data/globalLongShortAccountRatio"
        params = {
            'symbol': symbol,
            'period': period,
            'limit': limit
        }
        
        try:
            response = requests.get(url, params=params)
            response.raise_for_status()
            data = response.json()
            
            df = pd.DataFrame(data)
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            df['longShortRatio'] = df['longShortRatio'].astype(float)
            df['longAccount'] = df['longAccount'].astype(float)
            df['shortAccount'] = df['shortAccount'].astype(float)
            
            return df
            
        except Exception as e:
            print(f"获取多空比数据失败 {symbol}: {e}")
            return None
    
    def analyze_main_fund_signals(self, oi_data, ls_data, price_data):
        """分析主力资金信号"""
        # 合并数据
        merged_data = pd.merge(oi_data, ls_data, on='timestamp', how='inner')
        merged_data = pd.merge(merged_data, price_data[['timestamp', 'close']], on='timestamp', how='inner')
        
        # 计算变化率
        merged_data['oi_change_pct'] = merged_data['sumOpenInterest'].pct_change() * 100
        merged_data['ls_ratio_change_pct'] = merged_data['longShortRatio'].pct_change() * 100
        merged_data['price_change_pct'] = merged_data['close'].pct_change() * 100
        
        # 定义信号阈值
        oi_threshold = 2.0  # 持仓量变化阈值
        ls_threshold = 5.0  # 多空比变化阈值
        
        # 识别主力资金信号
        signals = []
        
        for i in range(1, len(merged_data)):
            row = merged_data.iloc[i]
            
            oi_change = row['oi_change_pct']
            ls_change = row['ls_ratio_change_pct']
            
            signal_type = None
            signal_desc = None
            
            # 根据理论分析信号
            if abs(oi_change) > oi_threshold:
                if oi_change > 0:  # 持仓上升
                    if ls_change < -ls_threshold:  # 多空比下降
                        signal_type = "主力做多建仓"
                        signal_desc = "持仓上升+多空比下降，主力大额做多"
                    elif ls_change > ls_threshold:  # 多空比上升
                        signal_type = "主力做空建仓"
                        signal_desc = "持仓上升+多空比上升，主力大额做空"
                else:  # 持仓下降
                    if ls_change > ls_threshold:  # 多空比上升
                        signal_type = "主力平多仓"
                        signal_desc = "持仓下降+多空比上升，主力平多单"
                    elif ls_change < -ls_threshold:  # 多空比下降
                        signal_type = "主力平空仓"
                        signal_desc = "持仓下降+多空比下降，主力平空单"
            
            if signal_type:
                signals.append({
                    'timestamp': row['timestamp'],
                    'signal_type': signal_type,
                    'signal_desc': signal_desc,
                    'oi_change_pct': oi_change,
                    'ls_change_pct': ls_change,
                    'price_change_pct': row['price_change_pct'],
                    'price': row['close'],
                    'open_interest': row['sumOpenInterest'],
                    'long_short_ratio': row['longShortRatio']
                })
        
        return pd.DataFrame(signals), merged_data
    
    def create_analysis_chart(self, symbol, merged_data, signals_df):
        """创建分析图表"""
        fig, axes = plt.subplots(4, 1, figsize=(15, 12))
        fig.suptitle(f'{symbol} 主力资金监测分析', fontsize=16, fontweight='bold')
        
        # 价格走势
        axes[0].plot(merged_data['timestamp'], merged_data['close'], 'b-', linewidth=1.5, label='价格')
        axes[0].set_title('价格走势', fontsize=12)
        axes[0].set_ylabel('价格 (USDT)', fontsize=10)
        axes[0].grid(True, alpha=0.3)
        axes[0].legend()
        
        # 持仓量
        axes[1].plot(merged_data['timestamp'], merged_data['sumOpenInterest'], 'g-', linewidth=1.5, label='持仓量')
        axes[1].set_title('持仓量变化', fontsize=12)
        axes[1].set_ylabel('持仓量', fontsize=10)
        axes[1].grid(True, alpha=0.3)
        axes[1].legend()
        
        # 多空比
        axes[2].plot(merged_data['timestamp'], merged_data['longShortRatio'], 'r-', linewidth=1.5, label='多空比')
        axes[2].axhline(y=1, color='gray', linestyle='--', alpha=0.5, label='平衡线')
        axes[2].set_title('多空持仓人数比', fontsize=12)
        axes[2].set_ylabel('多空比', fontsize=10)
        axes[2].grid(True, alpha=0.3)
        axes[2].legend()
        
        # 信号标记
        if not signals_df.empty:
            # 在价格图上标记信号
            for _, signal in signals_df.iterrows():
                color = 'red' if '做空' in signal['signal_type'] or '平多' in signal['signal_type'] else 'green'
                marker = 'v' if '做空' in signal['signal_type'] or '平多' in signal['signal_type'] else '^'
                
                axes[0].scatter(signal['timestamp'], signal['price'], 
                              color=color, marker=marker, s=100, alpha=0.8, zorder=5)
        
        # 信号统计
        signal_counts = signals_df['signal_type'].value_counts() if not signals_df.empty else pd.Series()
        
        # 创建信号统计表
        axes[3].axis('off')
        if not signal_counts.empty:
            table_data = []
            for signal_type, count in signal_counts.items():
                table_data.append([signal_type, count])
            
            table = axes[3].table(cellText=table_data,
                                colLabels=['信号类型', '次数'],
                                cellLoc='center',
                                loc='center',
                                colWidths=[0.6, 0.2])
            table.auto_set_font_size(False)
            table.set_fontsize(10)
            table.scale(1, 2)
            axes[3].set_title('主力资金信号统计', fontsize=12, pad=20)
        else:
            axes[3].text(0.5, 0.5, '暂无明显主力资金信号', 
                        ha='center', va='center', fontsize=12, transform=axes[3].transAxes)
            axes[3].set_title('主力资金信号统计', fontsize=12, pad=20)
        
        plt.tight_layout()
        return fig

    def generate_conclusion_report(self, all_results):
        """生成结论报告"""
        conclusions = []

        for symbol, data in all_results.items():
            signals_df = data['signals']
            merged_data = data['merged_data']

            if signals_df.empty:
                conclusion = f"{symbol}: 在分析期间内未发现明显的主力资金活动信号"
            else:
                # 统计各类信号
                signal_counts = signals_df['signal_type'].value_counts()

                # 分析最近的信号
                recent_signals = signals_df.tail(5)

                # 计算价格与信号的相关性
                price_changes = []
                for _, signal in signals_df.iterrows():
                    price_changes.append(signal['price_change_pct'])

                avg_price_impact = np.mean(price_changes) if price_changes else 0

                conclusion = f"""
{symbol} 主力资金分析结论:
- 总信号数量: {len(signals_df)}
- 主要信号类型: {signal_counts.index[0] if len(signal_counts) > 0 else '无'}
- 信号伴随的平均价格变化: {avg_price_impact:.2f}%
- 最近活跃度: {'高' if len(recent_signals) >= 3 else '中' if len(recent_signals) >= 1 else '低'}
"""

            conclusions.append(conclusion)

        return conclusions

    def run_analysis(self, period='5m', limit=500):
        """运行完整分析"""
        print("开始主力资金监测分析...")
        print("=" * 50)

        all_results = {}

        for symbol in self.symbols:
            print(f"\n正在分析 {symbol}...")

            # 获取数据
            kline_data = self.get_kline_data(symbol, period, limit)
            oi_data = self.get_open_interest_data(symbol, period, limit)
            ls_data = self.get_long_short_ratio_data(symbol, period, limit)

            if kline_data is None or oi_data is None or ls_data is None:
                print(f"  {symbol} 数据获取失败，跳过分析")
                continue

            # 分析信号
            signals_df, merged_data = self.analyze_main_fund_signals(oi_data, ls_data, kline_data)

            # 保存结果
            all_results[symbol] = {
                'signals': signals_df,
                'merged_data': merged_data,
                'kline_data': kline_data,
                'oi_data': oi_data,
                'ls_data': ls_data
            }

            # 创建图表
            fig = self.create_analysis_chart(symbol, merged_data, signals_df)

            # 保存图表
            if not os.path.exists('test'):
                os.makedirs('test')

            fig.savefig(f'test/{symbol}_主力资金分析.png', dpi=300, bbox_inches='tight')
            plt.close(fig)

            print(f"  {symbol} 分析完成，发现 {len(signals_df)} 个主力资金信号")

        # 生成结论报告
        conclusions = self.generate_conclusion_report(all_results)

        # 保存结论报告
        with open('test/主力资金监测结论报告.txt', 'w', encoding='utf-8') as f:
            f.write("主力资金监测方法结论报告\n")
            f.write("=" * 50 + "\n\n")
            f.write("分析方法论:\n")
            f.write("基于持仓量(OI)和多空比的综合分析法\n\n")
            f.write("信号识别规则:\n")
            f.write("1. 持仓上升 + 多空比下降 = 主力做多建仓\n")
            f.write("2. 持仓上升 + 多空比上升 = 主力做空建仓\n")
            f.write("3. 持仓下降 + 多空比上升 = 主力平多仓\n")
            f.write("4. 持仓下降 + 多空比下降 = 主力平空仓\n\n")
            f.write("分析结果:\n")
            f.write("-" * 30 + "\n")

            for conclusion in conclusions:
                f.write(conclusion + "\n")

        print("\n" + "=" * 50)
        print("分析完成！")
        print("图表已保存到 test/ 目录")
        print("结论报告已保存到 test/主力资金监测结论报告.txt")

        return all_results, conclusions


def main():
    """主函数"""
    monitor = MainFundMonitor()

    # 运行分析
    results, conclusions = monitor.run_analysis(period='5m', limit=500)

    # 打印结论
    print("\n主力资金监测结论:")
    print("=" * 50)
    for conclusion in conclusions:
        print(conclusion)


if __name__ == "__main__":
    main()
