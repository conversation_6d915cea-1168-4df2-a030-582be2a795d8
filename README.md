# Binance永续合约订单簿分析工具

这个项目用于分析Binance永续合约的订单簿数据，包括价差分析、深度分析、订单频率统计等功能。

## 功能特点

- 实时连接Binance永续合约WebSocket
- 维护本地订单簿
- 计算买卖价差统计（最大值、最小值、平均值等）
- 计算万五深度中间价区间内的挂单价值总和
- 分析订单频率和取消率
- 计算订单在簿中的平均时间

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

1. 确保您的`.env`文件中包含必要的Binance API密钥（如果需要）
2. 运行分析脚本：

```bash
python src/orderbook_analyzer.py
```

## 输出说明

程序会实时输出以下信息：
- 最近1分钟内的价差统计（最大值、最小值、平均值、标准差）
- 万五深度中间价区间内的挂单价值总和
- 订单频率（每分钟订单数）
- 订单在簿中的平均时间

## 注意事项

- 程序使用WebSocket连接实时获取数据
- 确保网络连接稳定
- 建议在服务器环境下运行以获得更稳定的数据 