#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Web系统演示脚本
展示系统的各项功能
"""

import requests
import json
import time
from datetime import datetime

class WebSystemDemo:
    """Web系统演示类"""
    
    def __init__(self, base_url="http://127.0.0.1:5001"):
        self.base_url = base_url
        self.session = requests.Session()
    
    def check_server_status(self):
        """检查服务器状态"""
        try:
            response = self.session.get(f"{self.base_url}/api/cache/status", timeout=5)
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 服务器运行正常")
                print(f"   缓存状态: {data['cache_size']}/{data['max_size']}")
                print(f"   缓存TTL: {data['ttl']}秒")
                return True
            else:
                print(f"❌ 服务器响应异常: {response.status_code}")
                return False
        except requests.exceptions.RequestException as e:
            print(f"❌ 无法连接服务器: {e}")
            print(f"请确保Web服务已启动: python web_app.py")
            return False
    
    def demo_analysis(self, symbol, period):
        """演示分析功能"""
        print(f"\n🔍 开始分析 {symbol} ({period})")
        print("-" * 40)
        
        try:
            start_time = time.time()
            
            response = self.session.post(
                f"{self.base_url}/api/analyze",
                json={"symbol": symbol, "period": period},
                timeout=30
            )
            
            end_time = time.time()
            
            if response.status_code == 200:
                data = response.json()
                
                print(f"✅ 分析完成 (耗时: {end_time - start_time:.2f}秒)")
                print(f"📊 分析结果:")
                print(f"   合约: {data['symbol']}")
                print(f"   周期: {data['period']}")
                print(f"   信号数量: {data['signals_count']}")
                print(f"   分析时间: {data['timestamp']}")
                
                # 显示报告摘要
                report = data['report']
                print(f"\n📋 分析报告:")
                print(f"   结论: {report['conclusion']}")
                
                if report['signal_summary']:
                    print(f"   信号统计:")
                    for signal_type, info in report['signal_summary'].items():
                        print(f"     • {signal_type}: {info['count']}次 (平均置信度: {info['avg_confidence']})")
                
                if report['recent_signals']:
                    print(f"   最近信号:")
                    for signal in report['recent_signals'][:3]:  # 只显示前3个
                        print(f"     • {signal['time']} - {signal['type']} ({signal['confidence_level']})")
                
                print(f"   投资建议:")
                for rec in report['recommendations'][:2]:  # 只显示前2个建议
                    print(f"     • {rec}")
                
                return True
                
            else:
                error_data = response.json()
                print(f"❌ 分析失败: {error_data.get('error', '未知错误')}")
                return False
                
        except requests.exceptions.RequestException as e:
            print(f"❌ 网络错误: {e}")
            return False
        except Exception as e:
            print(f"❌ 处理错误: {e}")
            return False
    
    def demo_cache_management(self):
        """演示缓存管理"""
        print(f"\n🗄️ 缓存管理演示")
        print("-" * 40)
        
        # 查看缓存状态
        try:
            response = self.session.get(f"{self.base_url}/api/cache/status")
            if response.status_code == 200:
                data = response.json()
                print(f"📊 当前缓存状态:")
                print(f"   缓存项数: {data['cache_size']}")
                print(f"   最大容量: {data['max_size']}")
                print(f"   TTL: {data['ttl']}秒")
            
            # 清除缓存
            print(f"\n🧹 清除缓存...")
            response = self.session.post(f"{self.base_url}/api/cache/clear")
            if response.status_code == 200:
                print(f"✅ 缓存已清除")
            else:
                print(f"❌ 清除缓存失败")
            
            # 再次查看状态
            response = self.session.get(f"{self.base_url}/api/cache/status")
            if response.status_code == 200:
                data = response.json()
                print(f"📊 清除后缓存状态:")
                print(f"   缓存项数: {data['cache_size']}")
            
        except Exception as e:
            print(f"❌ 缓存管理演示失败: {e}")
    
    def demo_multiple_symbols(self):
        """演示多个合约分析"""
        symbols = ['BTCUSDT', 'ETHUSDT', 'XRPUSDT']
        period = '5m'
        
        print(f"\n🔄 批量分析演示")
        print("-" * 40)
        
        results = {}
        
        for symbol in symbols:
            print(f"\n正在分析 {symbol}...")
            
            try:
                response = self.session.post(
                    f"{self.base_url}/api/analyze",
                    json={"symbol": symbol, "period": period},
                    timeout=30
                )
                
                if response.status_code == 200:
                    data = response.json()
                    results[symbol] = data['signals_count']
                    print(f"✅ {symbol}: {data['signals_count']} 个信号")
                else:
                    results[symbol] = "失败"
                    print(f"❌ {symbol}: 分析失败")
                    
            except Exception as e:
                results[symbol] = "错误"
                print(f"❌ {symbol}: {e}")
        
        # 汇总结果
        print(f"\n📊 批量分析汇总:")
        total_signals = 0
        for symbol, count in results.items():
            if isinstance(count, int):
                total_signals += count
                print(f"   {symbol}: {count} 个信号")
            else:
                print(f"   {symbol}: {count}")
        
        print(f"   总计: {total_signals} 个信号")
    
    def run_full_demo(self):
        """运行完整演示"""
        print("=" * 60)
        print("🚀 主力资金监测Web系统功能演示")
        print("=" * 60)
        print(f"演示时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"服务地址: {self.base_url}")
        
        # 1. 检查服务器状态
        if not self.check_server_status():
            return
        
        # 2. 单个合约分析演示
        self.demo_analysis('BTCUSDT', '5m')
        
        # 3. 不同周期演示
        self.demo_analysis('ETHUSDT', '1h')
        
        # 4. 山寨币演示
        self.demo_analysis('XRPUSDT', '15m')
        
        # 5. 缓存管理演示
        self.demo_cache_management()
        
        # 6. 批量分析演示
        self.demo_multiple_symbols()
        
        print("\n" + "=" * 60)
        print("🎉 演示完成！")
        print("\n💡 使用提示:")
        print(f"   • 浏览器访问: {self.base_url}")
        print("   • 选择合约和时间周期进行分析")
        print("   • 查看生成的图表和分析报告")
        print("   • 使用缓存功能提升查询速度")
        print("\n⚠️  注意事项:")
        print("   • 分析结果仅供参考，不构成投资建议")
        print("   • 建议结合其他指标进行综合判断")
        print("   • 任何交易都应设置合理的风险控制")
        print("=" * 60)


def main():
    """主函数"""
    demo = WebSystemDemo()
    
    try:
        demo.run_full_demo()
    except KeyboardInterrupt:
        print("\n\n演示被用户中断")
    except Exception as e:
        print(f"\n\n演示过程中发生错误: {e}")


if __name__ == '__main__':
    main()
