#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主力资金监测Web应用
基于Flask的Web服务，提供主力资金分析API
"""

from flask import Flask, render_template, request, jsonify, send_file
from flask_cors import CORS
import os
import sys
import json
import time
import threading
from datetime import datetime, timedelta
import base64
from io import BytesIO
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
import pandas as pd
import numpy as np
import requests
import pytz
from cachetools import TTLCache
import logging

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 添加当前目录到Python路径
sys.path.append('.')
sys.path.append('./test')

# 导入我们的分析模块
try:
    from test.improved_analysis import ImprovedMainFundMonitor
except ImportError:
    print("警告: 无法导入improved_analysis模块，将使用基础版本")
    # 使用基础版本但添加必要的方法
    from main_fund_monitor import MainFundMonitor

    class ImprovedMainFundMonitor(MainFundMonitor):
        def __init__(self):
            super().__init__()
            import pytz
            self.beijing_tz = pytz.timezone('Asia/Shanghai')

        def convert_to_beijing_time(self, timestamp):
            import pandas as pd
            if isinstance(timestamp, (int, float)):
                utc_time = pd.to_datetime(timestamp, unit='ms')
            else:
                utc_time = pd.to_datetime(timestamp)

            if utc_time.tz is None:
                utc_time = utc_time.tz_localize('UTC')

            beijing_time = utc_time.tz_convert('Asia/Shanghai')
            return beijing_time

        def format_beijing_time(self, timestamp):
            beijing_time = self.convert_to_beijing_time(timestamp)
            return beijing_time.strftime('%m-%d %H:%M (UTC+8)')

        def calculate_improved_confidence(self, oi_change, ls_change, oi_threshold=0.5, ls_threshold=2.0):
            """改进的置信度计算方法"""
            # 标准化变化值
            oi_score = abs(oi_change) / oi_threshold
            ls_score = abs(ls_change) / ls_threshold

            # 使用几何平均数，避免单一指标过度影响
            base_confidence = (oi_score * ls_score) ** 0.5

            # 信号一致性检查
            consistency_bonus = 1.0

            # 检查信号方向一致性
            if (oi_change > 0 and ls_change < 0) or (oi_change > 0 and ls_change > 0):
                # 持仓上升的情况
                consistency_bonus = 1.2
            elif (oi_change < 0 and ls_change > 0) or (oi_change < 0 and ls_change < 0):
                # 持仓下降的情况
                consistency_bonus = 1.2

            # 强度奖励：如果两个指标都很强
            if oi_score > 2.0 and ls_score > 2.0:
                consistency_bonus *= 1.3

            final_confidence = base_confidence * consistency_bonus

            # 限制最大置信度为2.0
            return min(final_confidence, 2.0)

        def get_confidence_level(self, confidence):
            """获取置信度等级"""
            if confidence >= 1.0:
                return "🟢 高置信度"
            elif confidence >= 0.6:
                return "🟡 中等置信度"
            elif confidence >= 0.3:
                return "🟠 低置信度"
            else:
                return "🔴 极低置信度"

        def analyze_main_fund_signals(self, oi_data, ls_data, price_data):
            # 转换所有时间戳为北京时间
            oi_data = oi_data.copy()
            ls_data = ls_data.copy()
            price_data = price_data.copy()

            oi_data['timestamp'] = oi_data['timestamp'].apply(self.convert_to_beijing_time)
            ls_data['timestamp'] = ls_data['timestamp'].apply(self.convert_to_beijing_time)
            price_data['timestamp'] = price_data['timestamp'].apply(self.convert_to_beijing_time)

            # 合并数据
            import pandas as pd
            merged_data = pd.merge(oi_data, ls_data, on='timestamp', how='inner')
            merged_data = pd.merge(merged_data, price_data[['timestamp', 'close']], on='timestamp', how='inner')

            # 计算变化率
            merged_data['oi_change_pct'] = merged_data['sumOpenInterest'].pct_change() * 100
            merged_data['ls_ratio_change_pct'] = merged_data['longShortRatio'].pct_change() * 100
            merged_data['price_change_pct'] = merged_data['close'].pct_change() * 100

            # 使用敏感的阈值
            oi_threshold = 0.5  # 持仓量变化阈值
            ls_threshold = 2.0  # 多空比变化阈值

            # 识别主力资金信号
            signals = []

            for i in range(1, len(merged_data)):
                row = merged_data.iloc[i]

                oi_change = row['oi_change_pct']
                ls_change = row['ls_ratio_change_pct']

                signal_type = None
                signal_desc = None

                # 根据理论分析信号
                if abs(oi_change) > oi_threshold:
                    if oi_change > 0:  # 持仓上升
                        if ls_change < -ls_threshold:  # 多空比下降
                            signal_type = "主力做多建仓"
                            signal_desc = f"持仓上升{oi_change:.2f}%+多空比下降{ls_change:.2f}%，主力大额做多"
                        elif ls_change > ls_threshold:  # 多空比上升
                            signal_type = "主力做空建仓"
                            signal_desc = f"持仓上升{oi_change:.2f}%+多空比上升{ls_change:.2f}%，主力大额做空"
                    else:  # 持仓下降
                        if ls_change > ls_threshold:  # 多空比上升
                            signal_type = "主力平多仓"
                            signal_desc = f"持仓下降{oi_change:.2f}%+多空比上升{ls_change:.2f}%，主力平多单"
                        elif ls_change < -ls_threshold:  # 多空比下降
                            signal_type = "主力平空仓"
                            signal_desc = f"持仓下降{oi_change:.2f}%+多空比下降{ls_change:.2f}%，主力平空单"

                if signal_type:
                    # 使用改进的置信度计算
                    confidence = self.calculate_improved_confidence(oi_change, ls_change, oi_threshold, ls_threshold)
                    confidence_level = self.get_confidence_level(confidence)

                    signals.append({
                        'timestamp': row['timestamp'],
                        'timestamp_str': self.format_beijing_time(row['timestamp']),
                        'signal_type': signal_type,
                        'signal_desc': signal_desc,
                        'confidence': confidence,
                        'confidence_level': confidence_level,
                        'oi_change_pct': oi_change,
                        'ls_change_pct': ls_change,
                        'price_change_pct': row['price_change_pct'],
                        'price': row['close'],
                        'open_interest': row['sumOpenInterest'],
                        'long_short_ratio': row['longShortRatio']
                    })

            return pd.DataFrame(signals), merged_data

app = Flask(__name__)
CORS(app)

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 全局缓存配置
CACHE_TTL = 300  # 5分钟缓存
cache = TTLCache(maxsize=100, ttl=CACHE_TTL)
cache_lock = threading.Lock()

# 支持的合约列表
SUPPORTED_SYMBOLS = [
    'BTCUSDT', 'ETHUSDT',  # 前两位
    '1000PEPEUSDT', '1000SHIBUSDT', 'ADAUSDT', 'AVAXUSDT', 'BCHUSDT', 
    'DOGEUSDT', 'DOTUSDT', 'ETCUSDT', 'LTCUSDT', 'MELANIAUSDT', 
    'OPUSDT', 'SANDUSDT', 'SOLUSDT', 'SUIUSDT', 'TONUSDT', 
    'TRUMPUSDT', 'UNIUSDT', 'XLMUSDT', 'XRPUSDT', 'CRVUSDT', 
    'XTZUSDT', 'DYDXUSDT'
]

# 支持的时间周期
SUPPORTED_PERIODS = ["5m", "15m", "30m", "1h", "2h", "4h", "6h", "12h", "1d"]

class WebMainFundMonitor(ImprovedMainFundMonitor):
    """Web版主力资金监测器"""
    
    def __init__(self):
        super().__init__()
        self.beijing_tz = pytz.timezone('Asia/Shanghai')
    
    def generate_chart_base64(self, symbol, merged_data, signals_df):
        """生成图表的base64编码"""
        try:
            fig = self.create_improved_chart(symbol, merged_data, signals_df)
            
            # 保存到内存中的字节流
            img_buffer = BytesIO()
            fig.savefig(img_buffer, format='png', dpi=150, bbox_inches='tight')
            img_buffer.seek(0)
            
            # 转换为base64
            img_base64 = base64.b64encode(img_buffer.getvalue()).decode()
            
            plt.close(fig)  # 释放内存
            img_buffer.close()
            
            return f"data:image/png;base64,{img_base64}"
            
        except Exception as e:
            logger.error(f"生成图表失败: {e}")
            return None
    
    def create_improved_chart(self, symbol, merged_data, signals_df):
        """创建改进版分析图表 - Web优化版"""
        fig, axes = plt.subplots(4, 1, figsize=(14, 12))
        fig.suptitle(f'{symbol} 主力资金监测分析 (UTC+8)', fontsize=16, fontweight='bold')
        
        # 价格走势
        axes[0].plot(merged_data['timestamp'], merged_data['close'], 'b-', linewidth=2, label='价格')
        
        # 添加信号标记
        if not signals_df.empty:
            colors = {'主力做多建仓': 'green', '主力做空建仓': 'red',
                     '主力平多仓': 'orange', '主力平空仓': 'blue'}
            markers = {'主力做多建仓': '^', '主力做空建仓': 'v',
                      '主力平多仓': 's', '主力平空仓': 'o'}

            # 用于图例的信号类型集合
            legend_signals = set()

            for _, signal in signals_df.iterrows():
                color = colors.get(signal['signal_type'], 'gray')
                marker = markers.get(signal['signal_type'], 'o')
                size = 80 + signal['confidence'] * 50

                # 为每种信号类型只添加一次图例
                label = signal['signal_type'] if signal['signal_type'] not in legend_signals else ""
                if signal['signal_type'] not in legend_signals:
                    legend_signals.add(signal['signal_type'])

                axes[0].scatter(signal['timestamp'], signal['price'],
                              color=color, marker=marker, s=size, alpha=0.8,
                              zorder=5, edgecolors='white', linewidth=2, label=label)
        
        axes[0].set_title('价格走势与主力资金信号', fontsize=12)
        axes[0].set_ylabel('价格 (USDT)', fontsize=10)
        axes[0].grid(True, alpha=0.3)
        axes[0].legend()
        
        # 持仓量
        axes[1].plot(merged_data['timestamp'], merged_data['sumOpenInterest'], 'g-', linewidth=2)
        axes[1].fill_between(merged_data['timestamp'], merged_data['sumOpenInterest'], alpha=0.3, color='green')
        axes[1].set_title('持仓量变化', fontsize=12)
        axes[1].set_ylabel('持仓量', fontsize=10)
        axes[1].grid(True, alpha=0.3)
        
        # 多空比
        axes[2].plot(merged_data['timestamp'], merged_data['longShortRatio'], 'r-', linewidth=2)
        axes[2].axhline(y=1, color='gray', linestyle='--', alpha=0.5, label='平衡线')
        axes[2].fill_between(merged_data['timestamp'], merged_data['longShortRatio'], 1,
                           where=(merged_data['longShortRatio'] > 1), alpha=0.3, color='green', label='多头占优')
        axes[2].fill_between(merged_data['timestamp'], merged_data['longShortRatio'], 1,
                           where=(merged_data['longShortRatio'] < 1), alpha=0.3, color='red', label='空头占优')
        axes[2].set_title('多空持仓人数比', fontsize=12)
        axes[2].set_ylabel('多空比', fontsize=10)
        axes[2].grid(True, alpha=0.3)
        axes[2].legend()
        
        # 信号统计表
        axes[3].axis('off')
        if not signals_df.empty:
            signal_counts = signals_df['signal_type'].value_counts()
            table_data = []
            for signal_type, count in signal_counts.items():
                avg_confidence = signals_df[signals_df['signal_type'] == signal_type]['confidence'].mean()
                table_data.append([signal_type, count, f"{avg_confidence:.2f}"])
            
            table = axes[3].table(cellText=table_data,
                                colLabels=['信号类型', '次数', '平均置信度'],
                                cellLoc='center',
                                loc='center',
                                colWidths=[0.5, 0.2, 0.3])
            table.auto_set_font_size(False)
            table.set_fontsize(10)
            table.scale(1, 2)
            axes[3].set_title('主力资金信号统计', fontsize=12, pad=20)
        else:
            axes[3].text(0.5, 0.5, '暂无明显主力资金信号', 
                        ha='center', va='center', fontsize=14, transform=axes[3].transAxes)
            axes[3].set_title('主力资金信号统计', fontsize=12, pad=20)
        
        # 格式化x轴时间显示
        for i in range(3):
            axes[i].tick_params(axis='x', rotation=45)
        
        plt.tight_layout()
        return fig
    
    def analyze_symbol_web(self, symbol, period='5m', limit=500):
        """Web版分析单个合约"""
        try:
            # 获取数据
            kline_data = self.get_kline_data(symbol, period, limit)
            oi_data = self.get_open_interest_data(symbol, period, limit)
            ls_data = self.get_long_short_ratio_data(symbol, period, limit)
            
            if kline_data is None or oi_data is None or ls_data is None:
                return None, "数据获取失败"
            
            # 分析信号
            signals_df, merged_data = self.analyze_main_fund_signals(oi_data, ls_data, kline_data)
            
            # 生成图表
            chart_base64 = self.generate_chart_base64(symbol, merged_data, signals_df)
            
            # 生成分析报告
            report = self.generate_analysis_report(symbol, signals_df, merged_data)
            
            return {
                'symbol': symbol,
                'period': period,
                'signals_count': len(signals_df),
                'signals': signals_df.to_dict('records') if not signals_df.empty else [],
                'chart': chart_base64,
                'report': report,
                'timestamp': datetime.now(self.beijing_tz).strftime('%Y-%m-%d %H:%M:%S')
            }, None
            
        except Exception as e:
            logger.error(f"分析 {symbol} 失败: {e}")
            return None, str(e)
    
    def generate_analysis_report(self, symbol, signals_df, merged_data):
        """生成分析报告"""
        report = {
            'symbol': symbol,
            'analysis_time': datetime.now(self.beijing_tz).strftime('%Y-%m-%d %H:%M:%S (UTC+8)'),
            'total_signals': len(signals_df),
            'signal_summary': {},
            'recent_signals': [],
            'recommendations': []
        }
        
        if signals_df.empty:
            report['conclusion'] = f"{symbol} 在分析期间内未发现明显的主力资金活动信号"
            report['recommendations'] = [
                "市场相对平静，主力资金活动较少",
                "建议继续观察，等待明确信号",
                "可关注其他更活跃的合约品种"
            ]
        else:
            # 信号统计
            signal_counts = signals_df['signal_type'].value_counts()
            for signal_type, count in signal_counts.items():
                avg_confidence = signals_df[signals_df['signal_type'] == signal_type]['confidence'].mean()
                report['signal_summary'][signal_type] = {
                    'count': int(count),
                    'avg_confidence': round(avg_confidence, 3)
                }
            
            # 最近信号
            recent_signals = signals_df.tail(5)
            for _, signal in recent_signals.iterrows():
                report['recent_signals'].append({
                    'time': signal['timestamp_str'],
                    'type': signal['signal_type'],
                    'confidence': round(signal['confidence'], 3),
                    'confidence_level': signal['confidence_level'],
                    'description': signal['signal_desc']
                })
            
            # 生成结论
            dominant_signal = signal_counts.index[0]
            avg_confidence = signals_df['confidence'].mean()
            
            report['conclusion'] = f"{symbol} 发现 {len(signals_df)} 个主力资金信号，主要类型为{dominant_signal}，平均置信度 {avg_confidence:.2f}"
            
            # 投资建议
            if avg_confidence >= 1.0:
                report['recommendations'] = [
                    "🟢 信号置信度较高，可考虑跟随主力方向",
                    "⚠️ 建议设置严格的止损点",
                    "📊 结合价格走势和成交量进行确认"
                ]
            else:
                report['recommendations'] = [
                    "🟡 信号置信度中等，需要谨慎对待",
                    "🔍 建议等待更强的确认信号",
                    "💡 可作为参考，但不宜重仓操作"
                ]
        
        return report

# 创建监测器实例
monitor = WebMainFundMonitor()

@app.route('/')
def index():
    """主页"""
    return render_template('index.html', 
                         symbols=SUPPORTED_SYMBOLS, 
                         periods=SUPPORTED_PERIODS)

@app.route('/api/analyze', methods=['POST'])
def analyze():
    """分析API接口"""
    try:
        data = request.get_json()
        symbol = data.get('symbol', 'BTCUSDT')
        period = data.get('period', '5m')
        
        # 验证参数
        if symbol not in SUPPORTED_SYMBOLS:
            return jsonify({'error': f'不支持的合约: {symbol}'}), 400
        
        if period not in SUPPORTED_PERIODS:
            return jsonify({'error': f'不支持的时间周期: {period}'}), 400
        
        # 检查缓存
        cache_key = f"{symbol}_{period}"
        with cache_lock:
            if cache_key in cache:
                logger.info(f"从缓存返回结果: {cache_key}")
                return jsonify(cache[cache_key])
        
        # 执行分析
        logger.info(f"开始分析: {symbol} - {period}")
        result, error = monitor.analyze_symbol_web(symbol, period)
        
        if error:
            return jsonify({'error': error}), 500
        
        # 存储到缓存
        with cache_lock:
            cache[cache_key] = result
        
        logger.info(f"分析完成: {symbol} - 发现 {result['signals_count']} 个信号")
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"API错误: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/cache/clear', methods=['POST'])
def clear_cache():
    """清除缓存"""
    try:
        with cache_lock:
            cache.clear()
        return jsonify({'message': '缓存已清除'})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/cache/status')
def cache_status():
    """缓存状态"""
    with cache_lock:
        return jsonify({
            'cache_size': len(cache),
            'max_size': cache.maxsize,
            'ttl': cache.ttl
        })

if __name__ == '__main__':
    # 确保模板目录存在
    if not os.path.exists('templates'):
        os.makedirs('templates')
    
    print("启动主力资金监测Web服务...")
    print(f"支持的合约数量: {len(SUPPORTED_SYMBOLS)}")
    print(f"支持的时间周期: {SUPPORTED_PERIODS}")
    print(f"缓存TTL: {CACHE_TTL}秒")
    
    app.run(debug=True, host='0.0.0.0', port=10001)
