#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动Web应用的脚本
包含环境检查和初始化
"""

import os
import sys
import subprocess
import importlib.util

def check_dependencies():
    """检查依赖是否安装"""
    required_packages = [
        'flask', 'flask_cors', 'matplotlib', 'pandas', 
        'numpy', 'requests', 'cachetools', 'pytz'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        if importlib.util.find_spec(package) is None:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"缺少以下依赖包: {', '.join(missing_packages)}")
        print("正在安装...")
        
        try:
            subprocess.check_call([
                sys.executable, '-m', 'pip', 'install'
            ] + missing_packages)
            print("依赖安装完成!")
        except subprocess.CalledProcessError:
            print("依赖安装失败，请手动安装")
            return False
    
    return True

def setup_environment():
    """设置环境"""
    # 确保templates目录存在
    if not os.path.exists('templates'):
        os.makedirs('templates')
        print("创建templates目录")
    
    # 确保test目录存在
    if not os.path.exists('test'):
        os.makedirs('test')
        print("创建test目录")
    
    # 设置matplotlib后端
    import matplotlib
    matplotlib.use('Agg')
    
    # 设置中文字体
    import matplotlib.pyplot as plt
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    
    print("环境设置完成")

def main():
    """主函数"""
    print("=" * 50)
    print("主力资金监测Web系统启动器")
    print("=" * 50)
    
    # 检查依赖
    if not check_dependencies():
        return
    
    # 设置环境
    setup_environment()
    
    # 启动Web应用
    print("\n正在启动Web服务...")
    print("访问地址: http://localhost:5000")
    print("按 Ctrl+C 停止服务")
    print("-" * 50)
    
    try:
        # 导入并运行web应用
        from web_app import app
        app.run(debug=True, host='0.0.0.0', port=5000)
    except KeyboardInterrupt:
        print("\n服务已停止")
    except Exception as e:
        print(f"启动失败: {e}")
        print("请检查错误信息并重试")

if __name__ == '__main__':
    main()
