cmake_minimum_required(VERSION 3.10)
project(binance_orderbook_analysis)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 查找必要的包
find_package(OpenSSL REQUIRED)
find_package(CURL REQUIRED)
find_package(Threads REQUIRED)

# 添加WebSocket++库
add_subdirectory(websocketpp)

# 添加主程序
add_executable(orderbook_analysis 
    src/main.cpp
    src/orderbook_analyzer.cpp
    src/websocket_client.cpp
)

# 链接库
target_link_libraries(orderbook_analysis
    PRIVATE
    OpenSSL::SSL
    OpenSSL::Crypto
    CURL::libcurl
    Threads::Threads
    websocketpp
)

# 包含目录
target_include_directories(orderbook_analysis
    PRIVATE
    ${CMAKE_SOURCE_DIR}/include
) 