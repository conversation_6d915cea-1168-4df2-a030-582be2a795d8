#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Web系统测试脚本
验证各个组件的功能
"""

import sys
import os
import requests
import time
import json

# 添加路径
sys.path.append('.')
sys.path.append('./test')

def test_imports():
    """测试导入"""
    print("测试模块导入...")
    
    try:
        import flask
        print(f"✅ Flask版本: {flask.__version__}")
    except ImportError as e:
        print(f"❌ Flask导入失败: {e}")
        return False
    
    try:
        import matplotlib
        print(f"✅ Matplotlib版本: {matplotlib.__version__}")
    except ImportError as e:
        print(f"❌ Matplotlib导入失败: {e}")
        return False
    
    try:
        from web_app import WebMainFundMonitor
        print("✅ WebMainFundMonitor导入成功")
    except ImportError as e:
        print(f"❌ WebMainFundMonitor导入失败: {e}")
        return False
    
    return True

def test_monitor_functionality():
    """测试监测器功能"""
    print("\n测试监测器功能...")
    
    try:
        from web_app import WebMainFundMonitor
        monitor = WebMainFundMonitor()
        
        # 测试数据获取
        print("测试数据获取...")
        kline_data = monitor.get_kline_data('BTCUSDT', '5m', 100)
        if kline_data is not None and len(kline_data) > 0:
            print("✅ K线数据获取成功")
        else:
            print("❌ K线数据获取失败")
            return False
        
        oi_data = monitor.get_open_interest_data('BTCUSDT', '5m', 100)
        if oi_data is not None and len(oi_data) > 0:
            print("✅ 持仓量数据获取成功")
        else:
            print("❌ 持仓量数据获取失败")
            return False
        
        ls_data = monitor.get_long_short_ratio_data('BTCUSDT', '5m', 100)
        if ls_data is not None and len(ls_data) > 0:
            print("✅ 多空比数据获取成功")
        else:
            print("❌ 多空比数据获取失败")
            return False
        
        # 测试分析功能
        print("测试分析功能...")
        result, error = monitor.analyze_symbol_web('BTCUSDT', '5m', 100)
        if result is not None:
            print("✅ 分析功能正常")
            print(f"   发现信号: {result['signals_count']}个")
        else:
            print(f"❌ 分析功能失败: {error}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 监测器测试失败: {e}")
        return False

def test_web_api():
    """测试Web API"""
    print("\n测试Web API...")
    
    # 启动测试服务器
    import threading
    import time
    from web_app import app
    
    def run_server():
        app.run(debug=False, host='127.0.0.1', port=5001, use_reloader=False)
    
    server_thread = threading.Thread(target=run_server, daemon=True)
    server_thread.start()
    
    # 等待服务器启动
    time.sleep(3)
    
    try:
        # 测试主页
        response = requests.get('http://127.0.0.1:5001/', timeout=10)
        if response.status_code == 200:
            print("✅ 主页访问正常")
        else:
            print(f"❌ 主页访问失败: {response.status_code}")
            return False
        
        # 测试缓存状态API
        response = requests.get('http://127.0.0.1:5001/api/cache/status', timeout=10)
        if response.status_code == 200:
            print("✅ 缓存状态API正常")
        else:
            print(f"❌ 缓存状态API失败: {response.status_code}")
            return False
        
        # 测试分析API
        print("测试分析API...")
        response = requests.post('http://127.0.0.1:5001/api/analyze', 
                               json={'symbol': 'BTCUSDT', 'period': '5m'},
                               timeout=30)
        if response.status_code == 200:
            data = response.json()
            print("✅ 分析API正常")
            print(f"   返回数据包含: {list(data.keys())}")
        else:
            print(f"❌ 分析API失败: {response.status_code}")
            if response.text:
                print(f"   错误信息: {response.text}")
            return False
        
        return True
        
    except requests.exceptions.RequestException as e:
        print(f"❌ API测试失败: {e}")
        return False

def test_chart_generation():
    """测试图表生成"""
    print("\n测试图表生成...")
    
    try:
        from web_app import WebMainFundMonitor
        import pandas as pd
        import numpy as np
        from datetime import datetime, timedelta
        
        monitor = WebMainFundMonitor()
        
        # 创建测试数据
        timestamps = pd.date_range(start=datetime.now() - timedelta(hours=24), 
                                 end=datetime.now(), freq='5min')
        
        merged_data = pd.DataFrame({
            'timestamp': timestamps,
            'close': np.random.normal(50000, 1000, len(timestamps)),
            'sumOpenInterest': np.random.normal(100000, 5000, len(timestamps)),
            'longShortRatio': np.random.normal(1.0, 0.2, len(timestamps))
        })
        
        signals_df = pd.DataFrame()  # 空信号数据
        
        # 测试图表生成
        chart_base64 = monitor.generate_chart_base64('BTCUSDT', merged_data, signals_df)
        
        if chart_base64 and chart_base64.startswith('data:image/png;base64,'):
            print("✅ 图表生成成功")
            print(f"   图表数据长度: {len(chart_base64)} 字符")
            return True
        else:
            print("❌ 图表生成失败")
            return False
            
    except Exception as e:
        print(f"❌ 图表生成测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("主力资金监测Web系统测试")
    print("=" * 60)
    
    tests = [
        ("模块导入", test_imports),
        ("监测器功能", test_monitor_functionality),
        ("图表生成", test_chart_generation),
        ("Web API", test_web_api),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统可以正常使用")
        print("\n启动命令:")
        print("python start_web_app.py")
        print("或者:")
        print("python web_app.py")
    else:
        print("⚠️  部分测试失败，请检查错误信息")
    
    print("=" * 60)

if __name__ == '__main__':
    main()
