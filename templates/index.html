<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>主力资金监测系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        
        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            margin: 20px auto;
            max-width: 1200px;
        }
        
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            border-radius: 20px 20px 0 0;
            text-align: center;
        }
        
        .control-panel {
            padding: 30px;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }
        
        .form-select, .btn {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }
        
        .form-select:focus {
            border-color: #4facfe;
            box-shadow: 0 0 0 0.2rem rgba(79, 172, 254, 0.25);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            padding: 12px 30px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }
        
        .btn-secondary {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            border: none;
            color: #333;
        }
        
        .loading-spinner {
            display: none;
            text-align: center;
            padding: 40px;
        }
        
        .results-container {
            padding: 30px;
            display: none;
        }
        
        .chart-container {
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .chart-container img {
            max-width: 100%;
            height: auto;
            border-radius: 10px;
        }
        
        .report-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .signal-card {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
            border-left: 5px solid #4facfe;
        }
        
        .confidence-high { border-left-color: #28a745; }
        .confidence-medium { border-left-color: #ffc107; }
        .confidence-low { border-left-color: #fd7e14; }
        .confidence-very-low { border-left-color: #dc3545; }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #4facfe;
        }
        
        .error-alert {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            border: none;
            border-radius: 10px;
        }
        
        .cache-info {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px 15px;
            border-radius: 20px;
            font-size: 0.8rem;
            z-index: 1000;
        }
        
        @media (max-width: 768px) {
            .main-container {
                margin: 10px;
                border-radius: 15px;
            }
            
            .header {
                padding: 20px;
                border-radius: 15px 15px 0 0;
            }
            
            .control-panel, .results-container {
                padding: 20px;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="cache-info" id="cacheInfo">
        <i class="fas fa-database"></i> 缓存: <span id="cacheSize">0</span>
    </div>

    <div class="container-fluid">
        <div class="main-container">
            <!-- 头部 -->
            <div class="header">
                <h1><i class="fas fa-chart-line"></i> 主力资金监测系统</h1>
                <p class="mb-0">基于持仓量与多空比的综合分析</p>
            </div>

            <!-- 控制面板 -->
            <div class="control-panel">
                <div class="row g-3 align-items-end">
                    <div class="col-md-4">
                        <label for="symbolSelect" class="form-label">
                            <i class="fas fa-coins"></i> 选择合约
                        </label>
                        <select class="form-select" id="symbolSelect">
                            {% for symbol in symbols %}
                            <option value="{{ symbol }}" {% if symbol == 'BTCUSDT' %}selected{% endif %}>
                                {{ symbol }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="col-md-3">
                        <label for="periodSelect" class="form-label">
                            <i class="fas fa-clock"></i> 时间周期
                        </label>
                        <select class="form-select" id="periodSelect">
                            {% for period in periods %}
                            <option value="{{ period }}" {% if period == '5m' %}selected{% endif %}>
                                {{ period }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="col-md-3">
                        <button class="btn btn-primary w-100" id="analyzeBtn">
                            <i class="fas fa-search"></i> 开始分析
                        </button>
                    </div>
                    
                    <div class="col-md-2">
                        <button class="btn btn-secondary w-100" id="clearCacheBtn">
                            <i class="fas fa-trash"></i> 清除缓存
                        </button>
                    </div>
                </div>
            </div>

            <!-- 加载状态 -->
            <div class="loading-spinner" id="loadingSpinner">
                <div class="spinner-border text-primary" role="status" style="width: 3rem; height: 3rem;">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <h4 class="mt-3">正在分析主力资金动向...</h4>
                <p class="text-muted">请稍候，正在获取数据并生成分析报告</p>
            </div>

            <!-- 结果容器 -->
            <div class="results-container" id="resultsContainer">
                <!-- 统计概览 -->
                <div class="stats-grid" id="statsGrid">
                    <!-- 动态生成统计卡片 -->
                </div>

                <!-- 图表展示 -->
                <div class="chart-container" id="chartContainer">
                    <!-- 动态插入图表 -->
                </div>

                <!-- 分析报告 -->
                <div class="report-section" id="reportSection">
                    <!-- 动态生成报告内容 -->
                </div>

                <!-- 最近信号 -->
                <div class="report-section" id="signalsSection">
                    <!-- 动态生成信号列表 -->
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        class MainFundMonitor {
            constructor() {
                this.initializeEventListeners();
                this.updateCacheInfo();
                // 每30秒更新一次缓存信息
                setInterval(() => this.updateCacheInfo(), 30000);
            }

            initializeEventListeners() {
                document.getElementById('analyzeBtn').addEventListener('click', () => this.analyze());
                document.getElementById('clearCacheBtn').addEventListener('click', () => this.clearCache());
                
                // 回车键触发分析
                document.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        this.analyze();
                    }
                });
            }

            async updateCacheInfo() {
                try {
                    const response = await fetch('/api/cache/status');
                    const data = await response.json();
                    document.getElementById('cacheSize').textContent = data.cache_size;
                } catch (error) {
                    console.error('获取缓存信息失败:', error);
                }
            }

            async analyze() {
                const symbol = document.getElementById('symbolSelect').value;
                const period = document.getElementById('periodSelect').value;
                
                this.showLoading();
                
                try {
                    const response = await fetch('/api/analyze', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ symbol, period })
                    });
                    
                    const data = await response.json();
                    
                    if (response.ok) {
                        this.displayResults(data);
                        this.updateCacheInfo();
                    } else {
                        this.showError(data.error || '分析失败');
                    }
                } catch (error) {
                    this.showError('网络错误: ' + error.message);
                }
            }

            async clearCache() {
                try {
                    const response = await fetch('/api/cache/clear', {
                        method: 'POST'
                    });
                    
                    const data = await response.json();
                    
                    if (response.ok) {
                        this.showSuccess('缓存已清除');
                        this.updateCacheInfo();
                    } else {
                        this.showError(data.error || '清除缓存失败');
                    }
                } catch (error) {
                    this.showError('网络错误: ' + error.message);
                }
            }

            showLoading() {
                document.getElementById('loadingSpinner').style.display = 'block';
                document.getElementById('resultsContainer').style.display = 'none';
                this.clearAlerts();
            }

            displayResults(data) {
                document.getElementById('loadingSpinner').style.display = 'none';
                document.getElementById('resultsContainer').style.display = 'block';
                
                this.renderStats(data);
                this.renderChart(data);
                this.renderReport(data);
                this.renderSignals(data);
                
                // 滚动到结果区域
                document.getElementById('resultsContainer').scrollIntoView({ 
                    behavior: 'smooth' 
                });
            }

            renderStats(data) {
                const statsGrid = document.getElementById('statsGrid');
                const report = data.report;
                
                statsGrid.innerHTML = `
                    <div class="stat-card">
                        <div class="stat-number">${data.signals_count}</div>
                        <div class="text-muted">发现信号</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${Object.keys(report.signal_summary).length}</div>
                        <div class="text-muted">信号类型</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${data.symbol}</div>
                        <div class="text-muted">分析合约</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${data.period}</div>
                        <div class="text-muted">时间周期</div>
                    </div>
                `;
            }

            renderChart(data) {
                const chartContainer = document.getElementById('chartContainer');
                
                if (data.chart) {
                    chartContainer.innerHTML = `
                        <h4><i class="fas fa-chart-area"></i> 主力资金分析图表</h4>
                        <img src="${data.chart}" alt="主力资金分析图表" class="img-fluid">
                        <div class="mt-3">
                            <small class="text-muted">
                                生成时间: ${data.timestamp} | 
                                数据来源: 币安永续合约API
                            </small>
                        </div>
                    `;
                } else {
                    chartContainer.innerHTML = `
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            图表生成失败，请重试
                        </div>
                    `;
                }
            }

            renderReport(data) {
                const reportSection = document.getElementById('reportSection');
                const report = data.report;
                
                let summaryHtml = '';
                for (const [signalType, info] of Object.entries(report.signal_summary)) {
                    summaryHtml += `
                        <div class="row mb-2">
                            <div class="col-6">${signalType}</div>
                            <div class="col-3 text-center">
                                <span class="badge bg-primary">${info.count}次</span>
                            </div>
                            <div class="col-3 text-center">
                                <span class="badge bg-info">${info.avg_confidence}</span>
                            </div>
                        </div>
                    `;
                }
                
                let recommendationsHtml = '';
                for (const rec of report.recommendations) {
                    recommendationsHtml += `<li class="mb-2">${rec}</li>`;
                }
                
                reportSection.innerHTML = `
                    <h4><i class="fas fa-file-alt"></i> 分析报告</h4>
                    
                    <div class="alert alert-info">
                        <h6><i class="fas fa-lightbulb"></i> 分析结论</h6>
                        <p class="mb-0">${report.conclusion}</p>
                    </div>
                    
                    ${summaryHtml ? `
                        <h6><i class="fas fa-chart-pie"></i> 信号统计</h6>
                        <div class="row mb-2 fw-bold border-bottom pb-2">
                            <div class="col-6">信号类型</div>
                            <div class="col-3 text-center">次数</div>
                            <div class="col-3 text-center">平均置信度</div>
                        </div>
                        ${summaryHtml}
                    ` : ''}
                    
                    <h6 class="mt-4"><i class="fas fa-recommendations"></i> 投资建议</h6>
                    <ul class="list-unstyled">
                        ${recommendationsHtml}
                    </ul>
                    
                    <small class="text-muted">
                        <i class="fas fa-clock"></i> 分析时间: ${report.analysis_time}
                    </small>
                `;
            }

            renderSignals(data) {
                const signalsSection = document.getElementById('signalsSection');
                const signals = data.report.recent_signals;
                
                if (signals.length === 0) {
                    signalsSection.innerHTML = `
                        <h4><i class="fas fa-signal"></i> 最近信号</h4>
                        <div class="alert alert-secondary">
                            <i class="fas fa-info-circle"></i>
                            暂无主力资金信号
                        </div>
                    `;
                    return;
                }
                
                let signalsHtml = '';
                for (const signal of signals) {
                    const confidenceClass = this.getConfidenceClass(signal.confidence);
                    signalsHtml += `
                        <div class="signal-card ${confidenceClass}">
                            <div class="row align-items-center">
                                <div class="col-md-3">
                                    <strong>${signal.time}</strong>
                                </div>
                                <div class="col-md-3">
                                    <span class="badge bg-dark">${signal.type}</span>
                                </div>
                                <div class="col-md-2">
                                    ${signal.confidence_level}
                                </div>
                                <div class="col-md-4">
                                    <small>${signal.description}</small>
                                </div>
                            </div>
                        </div>
                    `;
                }
                
                signalsSection.innerHTML = `
                    <h4><i class="fas fa-signal"></i> 最近信号</h4>
                    ${signalsHtml}
                `;
            }

            getConfidenceClass(confidence) {
                if (confidence >= 1.0) return 'confidence-high';
                if (confidence >= 0.6) return 'confidence-medium';
                if (confidence >= 0.3) return 'confidence-low';
                return 'confidence-very-low';
            }

            showError(message) {
                document.getElementById('loadingSpinner').style.display = 'none';
                this.showAlert(message, 'danger');
            }

            showSuccess(message) {
                this.showAlert(message, 'success');
            }

            showAlert(message, type) {
                this.clearAlerts();
                
                const alertDiv = document.createElement('div');
                alertDiv.className = `alert alert-${type} error-alert alert-dismissible fade show`;
                alertDiv.innerHTML = `
                    <i class="fas fa-${type === 'danger' ? 'exclamation-triangle' : 'check-circle'}"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;
                
                document.querySelector('.control-panel').appendChild(alertDiv);
                
                // 3秒后自动消失
                setTimeout(() => {
                    if (alertDiv.parentNode) {
                        alertDiv.remove();
                    }
                }, 3000);
            }

            clearAlerts() {
                const alerts = document.querySelectorAll('.alert');
                alerts.forEach(alert => alert.remove());
            }
        }

        // 初始化应用
        document.addEventListener('DOMContentLoaded', () => {
            new MainFundMonitor();
        });
    </script>
</body>
</html>
