#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复效果的脚本
验证置信度计算和图例显示
"""

import requests
import json
import time

def test_confidence_calculation():
    """测试置信度计算"""
    print("🔍 测试置信度计算...")
    
    try:
        response = requests.post(
            "http://127.0.0.1:5001/api/analyze",
            json={"symbol": "XRPUSDT", "period": "5m"},
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            
            print(f"✅ 分析成功")
            print(f"   合约: {data['symbol']}")
            print(f"   信号数量: {data['signals_count']}")
            
            if data['signals']:
                print(f"\n📊 信号详情:")
                for i, signal in enumerate(data['signals'][:3], 1):  # 只显示前3个
                    print(f"   信号 {i}:")
                    print(f"     时间: {signal['timestamp_str']}")
                    print(f"     类型: {signal['signal_type']}")
                    print(f"     置信度: {signal['confidence']:.3f}")
                    print(f"     等级: {signal['confidence_level']}")
                    print(f"     描述: {signal['signal_desc']}")
                    print()
                
                # 检查置信度是否不再是固定的0.5
                confidences = [s['confidence'] for s in data['signals']]
                unique_confidences = set(confidences)
                
                if len(unique_confidences) > 1 or (len(unique_confidences) == 1 and 0.5 not in unique_confidences):
                    print("✅ 置信度计算正常 - 不再是固定的0.5")
                else:
                    print("❌ 置信度计算仍有问题 - 仍然是固定值")
                
                # 检查置信度范围
                min_conf = min(confidences)
                max_conf = max(confidences)
                avg_conf = sum(confidences) / len(confidences)
                
                print(f"📈 置信度统计:")
                print(f"   最小值: {min_conf:.3f}")
                print(f"   最大值: {max_conf:.3f}")
                print(f"   平均值: {avg_conf:.3f}")
                
                return True
            else:
                print("⚠️  未发现信号，无法测试置信度")
                return False
        else:
            print(f"❌ API调用失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_chart_generation():
    """测试图表生成"""
    print("\n🎨 测试图表生成...")
    
    try:
        response = requests.post(
            "http://127.0.0.1:5001/api/analyze",
            json={"symbol": "BTCUSDT", "period": "15m"},
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            
            if data.get('chart'):
                chart_data = data['chart']
                if chart_data.startswith('data:image/png;base64,'):
                    print("✅ 图表生成成功")
                    print(f"   图表数据长度: {len(chart_data)} 字符")
                    
                    # 检查是否包含信号数据
                    if data['signals_count'] > 0:
                        print("✅ 图表应包含信号标记和图例")
                    else:
                        print("ℹ️  当前无信号，图表不包含信号标记")
                    
                    return True
                else:
                    print("❌ 图表格式错误")
                    return False
            else:
                print("❌ 图表生成失败")
                return False
        else:
            print(f"❌ API调用失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_multiple_symbols():
    """测试多个合约的置信度差异"""
    print("\n🔄 测试多个合约的置信度差异...")
    
    symbols = ['BTCUSDT', 'ETHUSDT', 'XRPUSDT']
    results = {}
    
    for symbol in symbols:
        try:
            response = requests.post(
                "http://127.0.0.1:5001/api/analyze",
                json={"symbol": symbol, "period": "5m"},
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                confidences = [s['confidence'] for s in data['signals']]
                
                results[symbol] = {
                    'signals_count': data['signals_count'],
                    'confidences': confidences,
                    'avg_confidence': sum(confidences) / len(confidences) if confidences else 0
                }
                
                print(f"   {symbol}: {data['signals_count']} 个信号, 平均置信度: {results[symbol]['avg_confidence']:.3f}")
            else:
                print(f"   {symbol}: API调用失败")
                
        except Exception as e:
            print(f"   {symbol}: 测试失败 - {e}")
    
    # 检查是否有置信度差异
    all_confidences = []
    for symbol_data in results.values():
        all_confidences.extend(symbol_data['confidences'])
    
    if len(set(all_confidences)) > 1:
        print("✅ 不同合约的置信度存在差异，计算正常")
        return True
    else:
        print("⚠️  所有置信度相同，可能仍有问题")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("🔧 主力资金监测系统修复验证")
    print("=" * 60)
    print("测试内容:")
    print("1. 置信度计算是否正确")
    print("2. 图表生成是否包含图例")
    print("3. 多个合约的置信度差异")
    print("-" * 60)
    
    # 等待服务器完全启动
    print("等待服务器启动...")
    time.sleep(2)
    
    tests = [
        ("置信度计算", test_confidence_calculation),
        ("图表生成", test_chart_generation),
        ("多合约差异", test_multiple_symbols),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"🎯 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有修复验证通过！")
        print("\n✅ 修复确认:")
        print("   • 置信度不再是固定的0.5")
        print("   • 置信度计算使用改进算法")
        print("   • 图表包含信号图例")
        print("   • 不同合约有不同的置信度")
    else:
        print("⚠️  部分测试失败，请检查修复效果")
    
    print("\n💡 使用建议:")
    print("   • 刷新浏览器页面: http://127.0.0.1:5001")
    print("   • 选择XRPUSDT进行测试（通常信号较多）")
    print("   • 查看图表中的信号标记和图例")
    print("   • 检查分析报告中的置信度数值")
    
    print("=" * 60)

if __name__ == '__main__':
    main()
