#pragma once

#include <string>
#include <vector>
#include <deque>
#include <map>
#include <mutex>
#include <chrono>

struct OrderBookEntry {
    double price;
    double quantity;
    std::chrono::system_clock::time_point timestamp;
};

class OrderBookAnalyzer {
public:
    OrderBookAnalyzer(const std::string& symbol);
    
    // 更新订单簿数据
    void updateOrderBook(const std::vector<std::pair<double, double>>& bids,
                        const std::vector<std::pair<double, double>>& asks);
    
    // 计算统计数据
    double getSpread() const;
    double getMidPrice() const;
    double getOrderBookDepth() const;
    
    // 计算订单统计
    struct OrderStats {
        double maxSpread;
        double minSpread;
        double avgSpread;
        double maxDepth;
        double minDepth;
        double avgDepth;
        double orderFrequency;
        double cancelRate;
        double avgOrderLifetime;
    };
    
    OrderStats calculateStats() const;
    
private:
    std::string symbol_;
    std::deque<OrderBookEntry> bids_;
    std::deque<OrderBookEntry> asks_;
    mutable std::mutex mutex_;
    
    // 统计数据
    std::vector<double> spreads_;
    std::vector<double> depths_;
    std::vector<std::chrono::system_clock::duration> orderLifetimes_;
    
    // 订单统计
    size_t totalOrders_ = 0;
    size_t cancelledOrders_ = 0;
    
    // 时间窗口
    static constexpr auto timeWindow = std::chrono::hours(1);
}; 