#pragma once

#include <websocketpp/client.hpp>
#include <websocketpp/config/asio_no_tls_client.hpp>
#include <functional>
#include <string>

class WebSocketClient {
public:
    using MessageCallback = std::function<void(const std::string&)>;
    
    WebSocketClient();
    ~WebSocketClient();
    
    void connect(const std::string& uri);
    void subscribe(const std::string& symbol);
    void setMessageCallback(MessageCallback callback);
    
private:
    using Client = websocketpp::client<websocketpp::config::asio_client>;
    
    Client client_;
    websocketpp::connection_hdl connection_;
    MessageCallback messageCallback_;
    
    void onMessage(websocketpp::connection_hdl hdl, Client::message_ptr msg);
    void onOpen(websocketpp::connection_hdl hdl);
    void onClose(websocketpp::connection_hdl hdl);
    void onError(websocketpp::connection_hdl hdl);
}; 