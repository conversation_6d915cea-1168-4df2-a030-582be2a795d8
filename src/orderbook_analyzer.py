import os
import json
import time
import websocket
import requests
import pandas as pd
import numpy as np
from datetime import datetime
from dotenv import load_dotenv
from collections import defaultdict
from typing import Dict, List, Tuple

class OrderbookAnalyzer:
    def __init__(self):
        load_dotenv()
        self.symbol = "BTCUSDT"
        self.ws_url = f"wss://fstream.binance.com/ws/{self.symbol.lower()}@depth"
        self.rest_url = f"https://fapi.binance.com/fapi/v1/depth?symbol={self.symbol}&limit=5000"
        self.orderbook = {"bids": {}, "asks": {}}
        self.last_update_id = 0
        self.cached_events = []
        self.spread_history = []
        self.order_timestamps = defaultdict(list)
        self.cancellation_count = 0
        self.total_orders = 0
        
    def get_snapshot(self) -> bool:
        """获取订单簿快照"""
        try:
            response = requests.get(self.rest_url)
            data = response.json()
            self.last_update_id = data['lastUpdateId']
            
            # 初始化订单簿
            for bid in data['bids']:
                self.orderbook['bids'][float(bid[0])] = float(bid[1])
            for ask in data['asks']:
                self.orderbook['asks'][float(ask[0])] = float(ask[1])
            return True
        except Exception as e:
            print(f"获取快照失败: {e}")
            return False

    def calculate_spread(self) -> float:
        """计算买卖价差"""
        if not self.orderbook['bids'] or not self.orderbook['asks']:
            return 0
        best_bid = max(self.orderbook['bids'].keys())
        best_ask = min(self.orderbook['asks'].keys())
        return best_ask - best_bid

    def calculate_depth_value(self) -> float:
        """计算万五深度中间价上下浮动万分之5的价格区间内所有挂单的价值总和"""
        if not self.orderbook['bids'] or not self.orderbook['asks']:
            return 0
            
        best_bid = max(self.orderbook['bids'].keys())
        best_ask = min(self.orderbook['asks'].keys())
        mid_price = (best_bid + best_ask) / 2
        
        lower_bound = mid_price * (1 - 0.0005)
        upper_bound = mid_price * (1 + 0.0005)
        
        total_value = 0
        
        # 计算买单价值
        for price, quantity in self.orderbook['bids'].items():
            if lower_bound <= price <= upper_bound:
                total_value += price * quantity
                
        # 计算卖单价值
        for price, quantity in self.orderbook['asks'].items():
            if lower_bound <= price <= upper_bound:
                total_value += price * quantity
                
        return total_value

    def update_orderbook(self, event: dict):
        """更新本地订单簿"""
        if event['u'] < self.last_update_id:
            return
            
        if event['U'] > self.last_update_id:
            print("需要重新获取快照")
            self.get_snapshot()
            
        # 更新买单
        for bid in event['b']:
            price, quantity = float(bid[0]), float(bid[1])
            if quantity == 0:
                self.orderbook['bids'].pop(price, None)
            else:
                self.orderbook['bids'][price] = quantity
                
        # 更新卖单
        for ask in event['a']:
            price, quantity = float(ask[0]), float(ask[1])
            if quantity == 0:
                self.orderbook['asks'].pop(price, None)
            else:
                self.orderbook['asks'][price] = quantity
                
        self.last_update_id = event['u']

    def on_message(self, ws, message):
        """处理WebSocket消息"""
        event = json.loads(message)
        self.update_orderbook(event)
        
        # 记录价差
        spread = self.calculate_spread()
        self.spread_history.append({
            'timestamp': datetime.now(),
            'spread': spread
        })
        
        # 记录订单时间戳
        for bid in event['b']:
            self.order_timestamps[float(bid[0])].append(datetime.now())
        for ask in event['a']:
            self.order_timestamps[float(ask[0])].append(datetime.now())

    def on_error(self, ws, error):
        print(f"WebSocket错误: {error}")

    def on_close(self, ws, close_status_code, close_msg):
        print("WebSocket连接关闭")

    def on_open(self, ws):
        print("WebSocket连接已建立")
        if not self.get_snapshot():
            ws.close()

    def analyze_results(self):
        """分析结果"""
        if not self.spread_history:
            return
            
        df = pd.DataFrame(self.spread_history)
        df.set_index('timestamp', inplace=True)
        
        # 计算最近1分钟的统计数据
        one_min_ago = datetime.now() - pd.Timedelta(minutes=1)
        recent_data = df[df.index >= one_min_ago]
        
        if not recent_data.empty:
            print("\n=== 最近1分钟价差统计 ===")
            print(f"最大价差: {recent_data['spread'].max():.2f}")
            print(f"最小价差: {recent_data['spread'].min():.2f}")
            print(f"平均价差: {recent_data['spread'].mean():.2f}")
            print(f"价差标准差: {recent_data['spread'].std():.2f}")
            
            # 计算订单簿深度
            depth_value = self.calculate_depth_value()
            print(f"\n万五深度中间价区间内挂单价值总和: {depth_value:.2f} USDT")
            
            # 计算订单频率和取消率
            total_orders = len(self.order_timestamps)
            if total_orders > 0:
                orders_per_minute = total_orders / (len(recent_data) / 60)
                print(f"\n订单频率: {orders_per_minute:.2f} 订单/分钟")
                
            # 计算订单在簿中的平均时间
            current_time = datetime.now()
            order_lifetimes = []
            for timestamps in self.order_timestamps.values():
                for ts in timestamps:
                    lifetime = (current_time - ts).total_seconds()
                    order_lifetimes.append(lifetime)
                    
            if order_lifetimes:
                avg_lifetime = np.mean(order_lifetimes)
                print(f"订单在簿中的平均时间: {avg_lifetime:.2f} 秒")

def main():
    analyzer = OrderbookAnalyzer()
    ws = websocket.WebSocketApp(
        analyzer.ws_url,
        on_message=analyzer.on_message,
        on_error=analyzer.on_error,
        on_close=analyzer.on_close,
        on_open=analyzer.on_open
    )
    
    ws.run_forever()
    
    # 分析结果
    analyzer.analyze_results()

if __name__ == "__main__":
    main() 