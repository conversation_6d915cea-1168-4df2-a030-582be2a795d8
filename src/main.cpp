#include "orderbook_analyzer.hpp"
#include "websocket_client.hpp"
#include <iostream>
#include <nlohmann/json.hpp>
#include <thread>
#include <chrono>

using json = nlohmann::json;

int main() {
    const std::string symbol = "btcusdt";
    OrderBookAnalyzer analyzer(symbol);
    WebSocketClient client;
    
    // 设置消息回调
    client.setMessageCallback([&analyzer](const std::string& message) {
        try {
            json data = json::parse(message);
            
            // 解析订单簿数据
            std::vector<std::pair<double, double>> bids;
            std::vector<std::pair<double, double>> asks;
            
            for (const auto& bid : data["b"]) {
                bids.emplace_back(
                    std::stod(bid[0].get<std::string>()),
                    std::stod(bid[1].get<std::string>())
                );
            }
            
            for (const auto& ask : data["a"]) {
                asks.emplace_back(
                    std::stod(ask[0].get<std::string>()),
                    std::stod(ask[1].get<std::string>())
                );
            }
            
            // 更新订单簿
            analyzer.updateOrderBook(bids, asks);
            
            // 每分钟输出一次统计数据
            static auto lastPrint = std::chrono::system_clock::now();
            auto now = std::chrono::system_clock::now();
            if (now - lastPrint >= std::chrono::minutes(1)) {
                auto stats = analyzer.calculateStats();
                
                std::cout << "\n=== 订单簿统计 ===" << std::endl;
                std::cout << "价差统计:" << std::endl;
                std::cout << "  最大价差: " << stats.maxSpread << std::endl;
                std::cout << "  最小价差: " << stats.minSpread << std::endl;
                std::cout << "  平均价差: " << stats.avgSpread << std::endl;
                
                std::cout << "\n订单簿深度统计:" << std::endl;
                std::cout << "  最大深度: " << stats.maxDepth << std::endl;
                std::cout << "  最小深度: " << stats.minDepth << std::endl;
                std::cout << "  平均深度: " << stats.avgDepth << std::endl;
                
                std::cout << "\n订单统计:" << std::endl;
                std::cout << "  订单频率: " << stats.orderFrequency << " 订单/秒" << std::endl;
                std::cout << "  取消率: " << stats.cancelRate * 100 << "%" << std::endl;
                std::cout << "  平均订单生命周期: " << stats.avgOrderLifetime << " 毫秒" << std::endl;
                
                lastPrint = now;
            }
            
        } catch (const std::exception& e) {
            std::cerr << "Error processing message: " << e.what() << std::endl;
        }
    });
    
    // 连接到Binance WebSocket
    client.connect("wss://fstream.binance.com/ws");
    client.subscribe(symbol);
    
    // 保持程序运行
    std::cout << "开始监控 " << symbol << " 的订单簿数据..." << std::endl;
    std::cout << "按Ctrl+C退出" << std::endl;
    
    while (true) {
        std::this_thread::sleep_for(std::chrono::seconds(1));
    }
    
    return 0;
} 