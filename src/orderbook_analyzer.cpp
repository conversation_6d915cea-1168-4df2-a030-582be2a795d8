#include "orderbook_analyzer.hpp"
#include <algorithm>
#include <numeric>
#include <cmath>

OrderBookAnalyzer::OrderBookAnalyzer(const std::string& symbol)
    : symbol_(symbol) {}

void OrderBookAnalyzer::updateOrderBook(
    const std::vector<std::pair<double, double>>& bids,
    const std::vector<std::pair<double, double>>& asks) {
    
    std::lock_guard<std::mutex> lock(mutex_);
    
    // 更新买盘
    bids_.clear();
    for (const auto& [price, quantity] : bids) {
        bids_.push_back({price, quantity, std::chrono::system_clock::now()});
    }
    
    // 更新卖盘
    asks_.clear();
    for (const auto& [price, quantity] : asks) {
        asks_.push_back({price, quantity, std::chrono::system_clock::now()});
    }
    
    // 计算并记录价差
    if (!bids_.empty() && !asks_.empty()) {
        double spread = asks_.front().price - bids_.front().price;
        spreads_.push_back(spread);
        
        // 计算订单簿深度
        double midPrice = getMidPrice();
        double depth = 0.0;
        
        for (const auto& bid : bids_) {
            if (std::abs(bid.price - midPrice) / midPrice <= 0.0005) {
                depth += bid.price * bid.quantity;
            }
        }
        
        for (const auto& ask : asks_) {
            if (std::abs(ask.price - midPrice) / midPrice <= 0.0005) {
                depth += ask.price * ask.quantity;
            }
        }
        
        depths_.push_back(depth);
    }
    
    // 清理过期数据
    auto now = std::chrono::system_clock::now();
    while (!spreads_.empty() && 
           (now - bids_.front().timestamp) > timeWindow) {
        spreads_.erase(spreads_.begin());
        depths_.erase(depths_.begin());
        bids_.pop_front();
        asks_.pop_front();
    }
}

double OrderBookAnalyzer::getSpread() const {
    std::lock_guard<std::mutex> lock(mutex_);
    if (bids_.empty() || asks_.empty()) return 0.0;
    return asks_.front().price - bids_.front().price;
}

double OrderBookAnalyzer::getMidPrice() const {
    std::lock_guard<std::mutex> lock(mutex_);
    if (bids_.empty() || asks_.empty()) return 0.0;
    return (bids_.front().price + asks_.front().price) / 2.0;
}

double OrderBookAnalyzer::getOrderBookDepth() const {
    std::lock_guard<std::mutex> lock(mutex_);
    if (depths_.empty()) return 0.0;
    return depths_.back();
}

OrderBookAnalyzer::OrderStats OrderBookAnalyzer::calculateStats() const {
    std::lock_guard<std::mutex> lock(mutex_);
    
    OrderStats stats{};
    
    if (!spreads_.empty()) {
        stats.maxSpread = *std::max_element(spreads_.begin(), spreads_.end());
        stats.minSpread = *std::min_element(spreads_.begin(), spreads_.end());
        stats.avgSpread = std::accumulate(spreads_.begin(), spreads_.end(), 0.0) / spreads_.size();
    }
    
    if (!depths_.empty()) {
        stats.maxDepth = *std::max_element(depths_.begin(), depths_.end());
        stats.minDepth = *std::min_element(depths_.begin(), depths_.end());
        stats.avgDepth = std::accumulate(depths_.begin(), depths_.end(), 0.0) / depths_.size();
    }
    
    // 计算订单频率（每秒）
    auto duration = std::chrono::duration_cast<std::chrono::seconds>(timeWindow).count();
    stats.orderFrequency = static_cast<double>(totalOrders_) / duration;
    
    // 计算取消率
    if (totalOrders_ > 0) {
        stats.cancelRate = static_cast<double>(cancelledOrders_) / totalOrders_;
    }
    
    // 计算平均订单生命周期
    if (!orderLifetimes_.empty()) {
        auto totalDuration = std::accumulate(
            orderLifetimes_.begin(),
            orderLifetimes_.end(),
            std::chrono::system_clock::duration(0)
        );
        stats.avgOrderLifetime = std::chrono::duration_cast<std::chrono::milliseconds>(
            totalDuration / orderLifetimes_.size()
        ).count();
    }
    
    return stats;
} 