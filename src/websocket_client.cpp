#include "websocket_client.hpp"
#include <iostream>
#include <nlohmann/json.hpp>

using json = nlohmann::json;

WebSocketClient::WebSocketClient() {
    client_.init_asio();
    client_.set_open_handler(std::bind(&WebSocketClient::onOpen, this, std::placeholders::_1));
    client_.set_close_handler(std::bind(&WebSocketClient::onClose, this, std::placeholders::_1));
    client_.set_message_handler(std::bind(&WebSocketClient::onMessage, this, std::placeholders::_1, std::placeholders::_2));
    client_.set_error_handler(std::bind(&WebSocketClient::onError, this, std::placeholders::_1));
}

WebSocketClient::~WebSocketClient() {
    if (connection_.lock()) {
        client_.close(connection_, websocketpp::close::status::normal, "Closing connection");
    }
}

void WebSocketClient::connect(const std::string& uri) {
    websocketpp::lib::error_code ec;
    Client::connection_ptr con = client_.get_connection(uri, ec);
    
    if (ec) {
        std::cerr << "Could not create connection: " << ec.message() << std::endl;
        return;
    }
    
    connection_ = con->get_handle();
    client_.connect(con);
    
    // 在新线程中运行
    std::thread([this]() {
        try {
            client_.run();
        } catch (const std::exception& e) {
            std::cerr << "Exception in WebSocket thread: " << e.what() << std::endl;
        }
    }).detach();
}

void WebSocketClient::subscribe(const std::string& symbol) {
    json subscribeMsg = {
        {"method", "SUBSCRIBE"},
        {"params", {
            symbol + "@depth@100ms"
        }},
        {"id", 1}
    };
    
    client_.send(connection_, subscribeMsg.dump(), websocketpp::frame::opcode::text);
}

void WebSocketClient::setMessageCallback(MessageCallback callback) {
    messageCallback_ = std::move(callback);
}

void WebSocketClient::onMessage(websocketpp::connection_hdl hdl, Client::message_ptr msg) {
    if (messageCallback_) {
        messageCallback_(msg->get_payload());
    }
}

void WebSocketClient::onOpen(websocketpp::connection_hdl hdl) {
    std::cout << "Connection opened" << std::endl;
}

void WebSocketClient::onClose(websocketpp::connection_hdl hdl) {
    std::cout << "Connection closed" << std::endl;
}

void WebSocketClient::onError(websocketpp::connection_hdl hdl) {
    std::cerr << "Connection error" << std::endl;
} 